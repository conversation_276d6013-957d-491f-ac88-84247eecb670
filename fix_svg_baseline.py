#!/usr/bin/env python3
"""
🔧 SCRIPT PER SISTEMARE SVG BASELINE
Applica le correzioni: stroke-width:3, rgb(0,0,0), fill semi-trasparente
"""

import json
import re
from pathlib import Path

def fix_svg_xml(xml_content):
    """Applica correzioni SVG per migliore visibilità"""
    
    # 1. Sostituisci stroke-width:1 con stroke-width:3
    xml_content = re.sub(r'stroke-width:1(?![0-9])', 'stroke-width:3', xml_content)
    
    # 2. Sostituisci colori grigi con nero pieno
    xml_content = re.sub(r'stroke:rgb\(2,2,2\)', 'stroke:rgb(0,0,0)', xml_content)
    xml_content = re.sub(r'stroke:rgb\(150,150,150\)', 'stroke:rgb(0,0,0)', xml_content)
    
    # 3. Aggiungi fill semi-trasparente dove c'è fill:none
    xml_content = re.sub(r'fill:none', 'fill:rgb(0,0,0);fill-opacity:0.3', xml_content)
    
    # 4. Migliora altri colori grigi
    xml_content = re.sub(r'rgb\((\d+),(\d+),(\d+)\)', lambda m: 
        'rgb(0,0,0)' if all(int(x) < 100 for x in m.groups()) else m.group(0), xml_content)
    
    return xml_content

def main():
    print("🔧 SISTEMAZIONE SVG BASELINE")
    print("=" * 50)
    
    # File di input e output
    input_file = "data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB.json"
    output_file = "data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json"
    
    print(f"📂 Input: {input_file}")
    print(f"📂 Output: {output_file}")
    
    # Carica dataset
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    print(f"📊 Esempi da processare: {len(data)}")
    
    # Applica correzioni
    fixed_count = 0
    for i, item in enumerate(data):
        original_xml = item['xml']
        fixed_xml = fix_svg_xml(original_xml)
        
        if fixed_xml != original_xml:
            item['xml'] = fixed_xml
            fixed_count += 1
        
        if (i + 1) % 100 == 0:
            print(f"✅ Processati: {i + 1}/{len(data)}")
    
    # Salva dataset corretto
    with open(output_file, 'w') as f:
        json.dump(data, f, indent=2)
    
    print("=" * 50)
    print("🎉 CORREZIONI APPLICATE!")
    print(f"✅ Esempi corretti: {fixed_count}/{len(data)}")
    print(f"📁 File salvato: {output_file}")
    print("=" * 50)
    
    # Mostra esempio di correzione
    if fixed_count > 0:
        print("\n📋 ESEMPIO CORREZIONE:")
        for item in data:
            if 'stroke-width:3' in item['xml']:
                print("PRIMA:")
                print("  stroke:rgb(2,2,2);stroke-width:1;fill:none")
                print("DOPO:")
                print("  stroke:rgb(0,0,0);stroke-width:3;fill:rgb(0,0,0);fill-opacity:0.3")
                break

if __name__ == "__main__":
    main()
