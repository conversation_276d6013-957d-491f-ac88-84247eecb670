#!/usr/bin/env python3
"""
Test SVG conversion per vedere se i colori sono visibili
"""

import json

def xml_to_svg(xml_content):
    """Converte XML content in SVG completo"""
    try:
        # Parsing delle linee XML
        lines = xml_content.strip().split('\n')
        svg_elements = []
        
        for line in lines:
            if '\t' in line:
                style_part, d_part = line.split('\t', 1)
                style = style_part.replace('style=', '').strip()
                d = d_part.replace('d=', '').strip()
                
                svg_elements.append(f'<path style="{style}" d="{d}"/>')
        
        # Crea SVG completo
        svg_content = f'''<svg width="400" height="400" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
<rect width="100%" height="100%" fill="white"/>
{chr(10).join(svg_elements)}
</svg>'''
        
        return svg_content
    except Exception as e:
        print(f"Errore conversione XML→SVG: {e}")
        return None

# Carica primo esempio
with open('data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB.json', 'r') as f:
    dataset = json.load(f)

first_example = dataset[0]
xml_content = first_example['xml']
caption = first_example['caption']

print("🔍 PRIMO ESEMPIO:")
print("=" * 50)
print("📝 Caption:")
print(caption)
print("\n🎨 XML Content:")
print(xml_content)

svg_content = xml_to_svg(xml_content)
print("\n🖼️ SVG Convertito:")
print(svg_content)

# Salva SVG per test
with open('test_example.svg', 'w') as f:
    f.write(svg_content)

print("\n✅ SVG salvato in test_example.svg")
print("📊 Controlla se l'SVG mostra correttamente:")
print("   - Sfondo bianco")
print("   - Elementi neri/grigi")
print("   - Forma della lettera P")
print("   - Palo verticale")
