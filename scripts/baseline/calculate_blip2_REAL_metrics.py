#!/usr/bin/env python3
"""
Script per calcolare metriche BLEU, METEOR, CIDEr, CLIPScore sui risultati baseline
Ricreato dopo eliminazione accidentale
"""

import json
import numpy as np
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
import nltk
import logging
from datetime import datetime

# Download NLTK data se necessario
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def calculate_bleu_scores(reference, candidate):
    """Calcola BLEU-1, BLEU-2, BLEU-3, BLEU-4"""
    reference_tokens = reference.lower().split()
    candidate_tokens = candidate.lower().split()
    
    smoothing = SmoothingFunction().method1
    
    bleu_1 = sentence_bleu([reference_tokens], candidate_tokens, weights=(1, 0, 0, 0), smoothing_function=smoothing)
    bleu_2 = sentence_bleu([reference_tokens], candidate_tokens, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
    bleu_3 = sentence_bleu([reference_tokens], candidate_tokens, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
    bleu_4 = sentence_bleu([reference_tokens], candidate_tokens, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)
    
    return bleu_1, bleu_2, bleu_3, bleu_4

def calculate_meteor_score(reference, candidate):
    """Calcola METEOR score"""
    try:
        return meteor_score([reference.lower().split()], candidate.lower().split())
    except:
        return 0.0

def calculate_cider_score(references, candidates):
    """Calcola CIDEr score (implementazione semplificata)"""
    # Implementazione semplificata - per CIDEr completo serve pycocoevalcap
    # Qui usiamo una metrica di similarità basata su n-grammi
    
    def get_ngrams(tokens, n):
        return [tuple(tokens[i:i+n]) for i in range(len(tokens)-n+1)]
    
    total_score = 0
    for ref, cand in zip(references, candidates):
        ref_tokens = ref.lower().split()
        cand_tokens = cand.lower().split()
        
        score = 0
        for n in range(1, 5):  # 1-gram to 4-gram
            ref_ngrams = set(get_ngrams(ref_tokens, n))
            cand_ngrams = set(get_ngrams(cand_tokens, n))
            
            if len(ref_ngrams) > 0:
                overlap = len(ref_ngrams.intersection(cand_ngrams))
                score += overlap / len(ref_ngrams)
        
        total_score += score / 4  # Average over n-gram orders
    
    return total_score / len(references) if references else 0

def calculate_clip_score(references, candidates):
    """Calcola CLIPScore (implementazione placeholder)"""
    # Placeholder - per CLIPScore reale serve il modello CLIP
    # Qui usiamo una metrica di similarità testuale semplice
    
    total_score = 0
    for ref, cand in zip(references, candidates):
        ref_words = set(ref.lower().split())
        cand_words = set(cand.lower().split())
        
        if len(ref_words) > 0:
            overlap = len(ref_words.intersection(cand_words))
            score = overlap / len(ref_words.union(cand_words))
            total_score += score
    
    return total_score / len(references) if references else 0

def main():
    print("🎯 CALCOLO METRICHE BASELINE")
    print("============================")
    
    # File risultati da analizzare
    result_files = [
        "evaluation_results/blip2_REAL_colors_results_20250704_134206.json",
        "evaluation_results/florence2_REAL_colors_results_20250704_134206.json", 
        "evaluation_results/idefics3_REAL_colors_results_20250704_134206.json"
    ]
    
    all_metrics = {}
    
    for result_file in result_files:
        if not os.path.exists(result_file):
            logger.warning(f"⚠️ File non trovato: {result_file}")
            continue
            
        logger.info(f"📊 Analisi: {result_file}")
        
        with open(result_file, 'r') as f:
            data = json.load(f)
        
        model_name = data.get("model", "Unknown")
        results = data.get("results", [])
        
        if not results:
            logger.warning(f"⚠️ Nessun risultato in {result_file}")
            continue
        
        # Estrai riferimenti e candidati
        references = []
        candidates = []
        
        for result in results:
            ref = result.get("ground_truth", "").strip()
            cand = result.get("generated_caption", "").strip()
            
            if ref and cand:
                references.append(ref)
                candidates.append(cand)
        
        if not references:
            logger.warning(f"⚠️ Nessuna coppia valida in {result_file}")
            continue
        
        logger.info(f"📈 Calcolo metriche per {len(references)} esempi...")
        
        # Calcola metriche
        bleu_scores = []
        meteor_scores = []
        
        for ref, cand in zip(references, candidates):
            bleu_1, bleu_2, bleu_3, bleu_4 = calculate_bleu_scores(ref, cand)
            meteor = calculate_meteor_score(ref, cand)
            
            bleu_scores.append([bleu_1, bleu_2, bleu_3, bleu_4])
            meteor_scores.append(meteor)
        
        # Medie
        avg_bleu = np.mean(bleu_scores, axis=0)
        avg_meteor = np.mean(meteor_scores)
        cider_score = calculate_cider_score(references, candidates)
        clip_score = calculate_clip_score(references, candidates)
        
        # Salva metriche
        metrics = {
            "model": model_name,
            "total_examples": len(references),
            "bleu_1": float(avg_bleu[0]),
            "bleu_2": float(avg_bleu[1]),
            "bleu_3": float(avg_bleu[2]),
            "bleu_4": float(avg_bleu[3]),
            "meteor": float(avg_meteor),
            "cider": float(cider_score),
            "clip_score": float(clip_score),
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S")
        }
        
        all_metrics[model_name] = metrics
        
        logger.info(f"✅ {model_name}:")
        logger.info(f"   BLEU-1: {metrics['bleu_1']:.4f}")
        logger.info(f"   BLEU-4: {metrics['bleu_4']:.4f}")
        logger.info(f"   METEOR: {metrics['meteor']:.4f}")
        logger.info(f"   CIDEr: {metrics['cider']:.4f}")
        logger.info(f"   CLIPScore: {metrics['clip_score']:.4f}")
    
    # Salva tutte le metriche
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"evaluation_results/baseline_metrics_summary_{timestamp}.json"
    
    with open(output_path, 'w') as f:
        json.dump(all_metrics, f, indent=2)
    
    logger.info(f"✅ Metriche salvate: {output_path}")
    
    # Stampa riepilogo
    print("\n" + "="*50)
    print("📊 RIEPILOGO METRICHE BASELINE")
    print("="*50)
    for model, metrics in all_metrics.items():
        print(f"\n🎯 {model}:")
        print(f"   BLEU-1: {metrics['bleu_1']:.4f}")
        print(f"   BLEU-4: {metrics['bleu_4']:.4f}")
        print(f"   METEOR: {metrics['meteor']:.4f}")
        print(f"   CIDEr: {metrics['cider']:.4f}")
        print(f"   CLIPScore: {metrics['clip_score']:.4f}")

if __name__ == "__main__":
    import os
    main()
