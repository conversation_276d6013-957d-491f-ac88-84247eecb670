#!/usr/bin/env python3
"""
Script per eseguire Florence-2 sui dati REAL colors per baseline - FIXED VERSION
"""

import json
import torch
from PIL import Image
from transformers import AutoProcessor, AutoModelForCausalLM
import os
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    print("🎯 FLORENCE-2 BASELINE EVALUATION - FIXED")
    print("==========================================")
    
    # Carica modello Florence-2
    logger.info("Caricamento Florence-2...")
    processor = AutoProcessor.from_pretrained("microsoft/Florence-2-large", trust_remote_code=True)
    model = AutoModelForCausalLM.from_pretrained(
        "microsoft/Florence-2-large", 
        torch_dtype=torch.float16,
        trust_remote_code=True,
        device_map="auto"
    )
    
    logger.info("✅ Florence-2 caricato")
    
    # Dataset
    dataset_path = "data/processed/xml_format_optimized/baseline_corrected_400_with_images.json"
    
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    logger.info(f"📊 Dataset caricato: {len(dataset)} esempi")
    
    # Risultati
    results = {
        "model": "Florence-2-FIXED",
        "dataset": "baseline_corrected_400_with_images.json",
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "total_examples": len(dataset),
        "successful_examples": 0,
        "results": []
    }
    
    # Elabora esempi
    for i, example in enumerate(dataset):
        try:
            # Carica immagine
            image_path = example.get("image_path", "")
            if not os.path.exists(image_path):
                logger.warning(f"⚠️ Immagine non trovata: {image_path}")
                continue
                
            image = Image.open(image_path).convert("RGB")
            
            # Prompt per Florence-2
            prompt = "<MORE_DETAILED_CAPTION>"
            
            # Processa input - FIXED VERSION
            inputs = processor(text=prompt, images=image, return_tensors="pt")
            if torch.cuda.is_available():
                inputs = {k: v.to("cuda") for k, v in inputs.items()}
                # FIXED: Converti pixel_values in float16 per compatibilità
                if "pixel_values" in inputs:
                    inputs["pixel_values"] = inputs["pixel_values"].to(torch.float16)
            
            # Genera caption
            with torch.no_grad():
                generated_ids = model.generate(
                    input_ids=inputs["input_ids"],
                    pixel_values=inputs["pixel_values"],
                    max_new_tokens=200,
                    do_sample=False,
                    num_beams=3
                )
                
                # Decodifica
                generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
                
                # Estrai solo la caption (rimuovi prompt)
                if prompt in generated_text:
                    generated_caption = generated_text.split(prompt)[1].strip()
                else:
                    generated_caption = generated_text.strip()
            
            # Salva risultato
            result = {
                "example_id": i,
                "image_path": image_path,
                "ground_truth": example.get("caption", ""),
                "generated_caption": generated_caption,
                "xml_content": example.get("xml_content", "")
            }
            
            results["results"].append(result)
            results["successful_examples"] += 1
            
            if (i + 1) % 50 == 0:
                logger.info(f"📈 Processati {i + 1}/{len(dataset)} esempi")
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"evaluation_results/florence2_FIXED_results_{timestamp}.json"
    
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ Risultati salvati: {output_path}")
    logger.info(f"📊 Esempi processati: {results['successful_examples']}/{results['total_examples']}")

if __name__ == "__main__":
    main()
