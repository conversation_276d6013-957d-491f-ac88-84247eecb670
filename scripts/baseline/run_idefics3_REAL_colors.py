#!/usr/bin/env python3
"""
Script per eseguire Idefics3 sui dati REAL colors per baseline
"""

import json
import torch
from PIL import Image
from transformers import AutoProcessor, AutoModelForVision2Seq
import os
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    print("🎯 IDEFICS3 BASELINE EVALUATION")
    print("===============================")
    
    # Carica modello Idefics3
    logger.info("Caricamento Idefics3...")
    processor = AutoProcessor.from_pretrained("HuggingFaceM4/Idefics3-8B-Llama3")
    model = AutoModelForVision2Seq.from_pretrained(
        "HuggingFaceM4/Idefics3-8B-Llama3", 
        torch_dtype=torch.float16,
        device_map="auto"
    )
    
    logger.info("✅ Idefics3 caricato")
    
    # Dataset
    dataset_path = "data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json"
    
    with open(dataset_path, 'r') as f:
        dataset = json.load(f)
    
    logger.info(f"📊 Dataset caricato: {len(dataset)} esempi")
    
    # Risultati
    results = {
        "model": "Idefics3",
        "dataset": "baseline_corrected_400_with_images.json",
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "total_examples": len(dataset),
        "successful_examples": 0,
        "results": []
    }
    
    # Elabora esempi
    for i, example in enumerate(dataset):
        try:
            # Carica immagine
            image_path = example.get("image_path", "")
            if not os.path.exists(image_path):
                logger.warning(f"⚠️ Immagine non trovata: {image_path}")
                continue
                
            image = Image.open(image_path).convert("RGB")
            
            # Prepara input per Idefics3
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image"},
                        {"type": "text", "text": "Describe this image in detail, focusing on shapes, colors, and visual elements."}
                    ]
                }
            ]
            
            # Processa input
            inputs = processor.apply_chat_template(messages, images=[image], return_tensors="pt")
            inputs = {k: v.to(model.device) for k, v in inputs.items()}
            
            # Genera caption
            with torch.no_grad():
                generated_ids = model.generate(
                    **inputs,
                    max_new_tokens=200,
                    do_sample=False
                )
                
                # Decodifica solo i nuovi token
                generated_text = processor.decode(
                    generated_ids[0][inputs["input_ids"].shape[1]:], 
                    skip_special_tokens=True
                )
            
            # Salva risultato
            result = {
                "example_id": i,
                "image_path": image_path,
                "ground_truth": example.get("caption", ""),
                "generated_caption": generated_text.strip(),
                "xml_content": example.get("xml_content", "")
            }
            
            results["results"].append(result)
            results["successful_examples"] += 1
            
            if (i + 1) % 50 == 0:
                logger.info(f"📈 Processati {i + 1}/{len(dataset)} esempi")
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"evaluation_results/idefics3_REAL_colors_results_{timestamp}.json"
    
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ Risultati salvati: {output_path}")
    logger.info(f"📊 Esempi processati: {results['successful_examples']}/{results['total_examples']}")

if __name__ == "__main__":
    main()
