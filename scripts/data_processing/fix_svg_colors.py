#!/usr/bin/env python3
"""
Fix SVG color format issues in the dataset
Il problema: fill:0,0,0 non è un formato colore valido
Soluzione: Convertire in fill:#000000 o fill:rgb(0,0,0)
"""

import json
import re
import os
import logging
from tqdm import tqdm

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_svg_color_format(svg_content):
    """
    Corregge i formati colore non validi negli SVG
    
    Problemi da correggere:
    - fill:0,0,0 -> fill:#000000
    - fill:255,255,255 -> fill:#ffffff
    - fill:r,g,b -> fill:rgb(r,g,b)
    """
    if not svg_content:
        return svg_content
    
    # Pattern per trovare fill:r,g,b
    fill_pattern = r'fill:(\d+),(\d+),(\d+)'
    
    def replace_fill(match):
        r, g, b = match.groups()
        r, g, b = int(r), int(g), int(b)
        
        # Converti in formato esadecimale
        hex_color = f"#{r:02x}{g:02x}{b:02x}"
        return f"fill:{hex_color}"
    
    # Sostituisci tutti i fill:r,g,b con fill:#rrggbb
    fixed_svg = re.sub(fill_pattern, replace_fill, svg_content)
    
    # Pattern per stroke:r,g,b
    stroke_pattern = r'stroke:(\d+),(\d+),(\d+)'
    
    def replace_stroke(match):
        r, g, b = match.groups()
        r, g, b = int(r), int(g), int(b)
        hex_color = f"#{r:02x}{g:02x}{b:02x}"
        return f"stroke:{hex_color}"
    
    # Sostituisci tutti i stroke:r,g,b con stroke:#rrggbb
    fixed_svg = re.sub(stroke_pattern, replace_stroke, fixed_svg)
    
    return fixed_svg

def analyze_svg_colors(svg_content):
    """Analizza i colori presenti nell'SVG per debug"""
    if not svg_content:
        return {}
    
    # Trova tutti i pattern di colore
    fill_patterns = re.findall(r'fill:([^;]+)', svg_content)
    stroke_patterns = re.findall(r'stroke:([^;]+)', svg_content)
    
    return {
        'fill_colors': fill_patterns,
        'stroke_colors': stroke_patterns
    }

def fix_dataset_svg_colors(input_file, output_file):
    """Corregge i colori SVG in tutto il dataset"""
    
    logger.info(f"🔧 Caricamento dataset: {input_file}")
    
    with open(input_file, 'r') as f:
        dataset = json.load(f)
    
    logger.info(f"📊 Dataset caricato: {len(dataset)} esempi")
    
    # Statistiche
    fixed_count = 0
    error_count = 0
    color_stats = {}
    
    # Processa ogni esempio
    for i, example in enumerate(tqdm(dataset, desc="Fixing SVG colors")):
        try:
            svg_content = example.get('xml', '')
            
            if not svg_content:
                continue
            
            # Analizza colori originali
            original_colors = analyze_svg_colors(svg_content)
            
            # Correggi colori
            fixed_svg = fix_svg_color_format(svg_content)
            
            # Verifica se ci sono stati cambiamenti
            if fixed_svg != svg_content:
                example['xml'] = fixed_svg
                example['svg_colors_fixed'] = True
                fixed_count += 1
                
                # Log dei cambiamenti per debug
                if i < 5:  # Solo primi 5 esempi per debug
                    logger.info(f"🔧 Esempio {i} ({example.get('id', 'unknown')}):")
                    logger.info(f"   Colori originali: {original_colors}")
                    
                    fixed_colors = analyze_svg_colors(fixed_svg)
                    logger.info(f"   Colori corretti: {fixed_colors}")
            
            # Raccogli statistiche colori
            colors = analyze_svg_colors(fixed_svg)
            for color in colors['fill_colors']:
                color_stats[f"fill:{color}"] = color_stats.get(f"fill:{color}", 0) + 1
            for color in colors['stroke_colors']:
                color_stats[f"stroke:{color}"] = color_stats.get(f"stroke:{color}", 0) + 1
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            error_count += 1
    
    # Salva dataset corretto
    logger.info(f"💾 Salvataggio dataset corretto: {output_file}")
    
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(dataset, f, indent=2)
    
    # Statistiche finali
    logger.info(f"\n📊 STATISTICHE CORREZIONE:")
    logger.info(f"   Esempi totali: {len(dataset)}")
    logger.info(f"   Esempi corretti: {fixed_count}")
    logger.info(f"   Errori: {error_count}")
    logger.info(f"   Percentuale corretta: {fixed_count/len(dataset)*100:.1f}%")
    
    logger.info(f"\n🎨 COLORI PIÙ COMUNI:")
    sorted_colors = sorted(color_stats.items(), key=lambda x: x[1], reverse=True)
    for color, count in sorted_colors[:10]:
        logger.info(f"   {color}: {count} occorrenze")
    
    return {
        'total_examples': len(dataset),
        'fixed_examples': fixed_count,
        'errors': error_count,
        'color_stats': color_stats
    }

def test_svg_fix():
    """Test della correzione SVG"""
    test_svg = """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:0,0,0;stroke:255,0,0;stroke-width:1;opacity:1" d="M114,338 C82,338,57,363,57,394" />
<path style="fill:128,128,128;stroke:None;stroke-width:1;opacity:1" d="M394,338 C363,338,337,363,337,394" />
</svg>"""
    
    logger.info("🧪 Test correzione SVG:")
    logger.info(f"Originale: {test_svg}")
    
    fixed = fix_svg_color_format(test_svg)
    logger.info(f"Corretto: {fixed}")
    
    original_colors = analyze_svg_colors(test_svg)
    fixed_colors = analyze_svg_colors(fixed)
    
    logger.info(f"Colori originali: {original_colors}")
    logger.info(f"Colori corretti: {fixed_colors}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Corregge formati colore SVG nel dataset")
    parser.add_argument('--input', 
                       default='data/processed/xml_format_optimized/baseline_t7_corrected_400_with_images.json',
                       help='File dataset input')
    parser.add_argument('--output',
                       default='data/processed/xml_format_optimized/baseline_t7_corrected_400_colors_fixed.json',
                       help='File dataset output con colori corretti')
    parser.add_argument('--test', action='store_true',
                       help='Esegui solo test di correzione')
    
    args = parser.parse_args()
    
    if args.test:
        test_svg_fix()
        return
    
    # Correggi dataset
    stats = fix_dataset_svg_colors(args.input, args.output)
    
    logger.info(f"\n🎉 CORREZIONE COMPLETATA!")
    logger.info(f"📁 File output: {args.output}")
    logger.info(f"🔧 Esempi corretti: {stats['fixed_examples']}/{stats['total_examples']}")

if __name__ == "__main__":
    main()
