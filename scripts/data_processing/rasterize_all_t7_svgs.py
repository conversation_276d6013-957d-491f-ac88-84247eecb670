#!/usr/bin/env python3
"""
Script per rasterizzare tutti gli SVG del dataset T7 (400 esempi)
"""

import json
import os
import sys
import logging
from pathlib import Path
import subprocess
from tqdm import tqdm
import tempfile

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_dependencies():
    """Verifica dipendenze per rasterizzazione"""
    logger.info("🔍 Verifica dipendenze per rasterizzazione SVG...")
    
    # Verifica Inkscape
    try:
        result = subprocess.run(['inkscape', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"✅ Inkscape trovato: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    # Verifica cairosvg (alternativa Python)
    try:
        import cairosvg
        logger.info(f"✅ CairoSVG trovato: {cairosvg.__version__}")
        return True
    except ImportError:
        pass
    
    # Verifica wkhtmltopdf/wkhtmltoimage
    try:
        result = subprocess.run(['wkhtmltoimage', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"✅ wkhtmltoimage trovato")
            return True
    except FileNotFoundError:
        pass
    
    logger.error("❌ Nessun tool di rasterizzazione trovato!")
    logger.error("Installa uno di: inkscape, cairosvg, wkhtmltoimage")
    return False

def rasterize_svg_inkscape(svg_content, output_path, width=512, height=512):
    """Rasterizza SVG usando Inkscape con sfondo bianco"""
    try:
        # Crea file temporaneo SVG
        with tempfile.NamedTemporaryFile(mode='w', suffix='.svg', delete=False) as temp_svg:
            temp_svg.write(svg_content)
            temp_svg_path = temp_svg.name

        # Comando Inkscape con sfondo bianco
        cmd = [
            'inkscape',
            '--export-type=png',
            f'--export-filename={output_path}',
            f'--export-width={width}',
            f'--export-height={height}',
            '--export-background=white',  # Sfondo bianco
            '--export-background-opacity=1.0',  # Opacità completa
            temp_svg_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        # Cleanup
        os.unlink(temp_svg_path)

        if result.returncode == 0 and os.path.exists(output_path):
            return True
        else:
            logger.error(f"Inkscape error: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"Errore Inkscape: {e}")
        return False

def rasterize_svg_cairosvg(svg_content, output_path, width=512, height=512):
    """Rasterizza SVG usando CairoSVG con sfondo bianco"""
    try:
        import cairosvg
        from PIL import Image
        import io

        # Rasterizza SVG con sfondo bianco
        png_data = cairosvg.svg2png(
            bytestring=svg_content.encode('utf-8'),
            output_width=width,
            output_height=height,
            background_color='white'
        )

        # Carica immagine PNG
        img = Image.open(io.BytesIO(png_data))

        # Crea sfondo bianco
        background = Image.new('RGB', (width, height), 'white')

        # Se l'immagine ha canale alpha, composita su sfondo bianco
        if img.mode in ('RGBA', 'LA'):
            background.paste(img, mask=img.split()[-1])  # Usa canale alpha come mask
        else:
            # Se non ha alpha, converte a RGB e incolla
            img_rgb = img.convert('RGB')
            background.paste(img_rgb)

        # Salva con sfondo bianco
        background.save(output_path, 'PNG')

        return os.path.exists(output_path)

    except Exception as e:
        logger.error(f"Errore CairoSVG: {e}")
        return False

def rasterize_svg_wkhtmltoimage(svg_content, output_path, width=512, height=512):
    """Rasterizza SVG usando wkhtmltoimage con sfondo bianco"""
    try:
        # Crea HTML wrapper per SVG con sfondo bianco
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{
                    margin: 0;
                    padding: 0;
                    background-color: white;
                    width: {width}px;
                    height: {height}px;
                }}
                svg {{
                    width: {width}px;
                    height: {height}px;
                    background-color: white;
                }}
            </style>
        </head>
        <body>
            {svg_content}
        </body>
        </html>
        """
        
        # Crea file temporaneo HTML
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as temp_html:
            temp_html.write(html_content)
            temp_html_path = temp_html.name
        
        # Comando wkhtmltoimage
        cmd = [
            'wkhtmltoimage',
            '--width', str(width),
            '--height', str(height),
            '--format', 'png',
            '--quiet',
            temp_html_path,
            output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # Cleanup
        os.unlink(temp_html_path)
        
        return result.returncode == 0 and os.path.exists(output_path)
        
    except Exception as e:
        logger.error(f"Errore wkhtmltoimage: {e}")
        return False

def rasterize_svg(svg_content, output_path, width=512, height=512):
    """Rasterizza SVG usando il primo tool disponibile"""
    
    # Prova Inkscape (migliore qualità)
    if rasterize_svg_inkscape(svg_content, output_path, width, height):
        return True
    
    # Prova CairoSVG
    if rasterize_svg_cairosvg(svg_content, output_path, width, height):
        return True
    
    # Prova wkhtmltoimage
    if rasterize_svg_wkhtmltoimage(svg_content, output_path, width, height):
        return True
    
    return False

def process_dataset(dataset_file, output_dir, max_examples=None):
    """Processa dataset e rasterizza tutti gli SVG"""
    logger.info(f"📊 Caricamento dataset: {dataset_file}")
    
    with open(dataset_file, 'r') as f:
        dataset = json.load(f)
    
    total_examples = len(dataset)
    if max_examples:
        dataset = dataset[:max_examples]
        total_examples = len(dataset)
    
    logger.info(f"📈 Esempi da processare: {total_examples}")
    
    # Crea directory output
    os.makedirs(output_dir, exist_ok=True)
    
    # Statistiche
    success_count = 0
    failed_count = 0
    skipped_count = 0
    
    # Processa ogni esempio
    for i, example in enumerate(tqdm(dataset, desc="Rasterizzazione SVG")):
        try:
            example_id = example.get('id', f'example_{i}')
            svg_content = example.get('xml', '')
            
            if not svg_content:
                logger.warning(f"⚠️ Esempio {i}: SVG vuoto")
                failed_count += 1
                continue
            
            # Path output
            output_path = os.path.join(output_dir, f"{example_id}.png")
            
            # Skip se già esiste
            if os.path.exists(output_path):
                skipped_count += 1
                continue
            
            # Rasterizza
            if rasterize_svg(svg_content, output_path):
                success_count += 1
                
                # Aggiorna esempio con path immagine
                example['image_path'] = output_path
                example['rasterized_path'] = output_path
            else:
                logger.error(f"❌ Fallita rasterizzazione esempio {i}: {example_id}")
                failed_count += 1
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            failed_count += 1
    
    # Statistiche finali
    logger.info(f"🎯 RASTERIZZAZIONE COMPLETATA:")
    logger.info(f"  ✅ Successi: {success_count}")
    logger.info(f"  ❌ Fallimenti: {failed_count}")
    logger.info(f"  ⏭️ Saltati (già esistenti): {skipped_count}")
    logger.info(f"  📊 Totale: {total_examples}")
    
    # Salva dataset aggiornato
    if success_count > 0:
        # Filtra solo esempi con immagini
        dataset_with_images = [ex for ex in dataset if 'image_path' in ex and os.path.exists(ex['image_path'])]
        
        output_dataset_file = os.path.join(os.path.dirname(dataset_file),
                                         f"baseline_corrected_{len(dataset_with_images)}_with_images.json")
        
        with open(output_dataset_file, 'w') as f:
            json.dump(dataset_with_images, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📄 Dataset aggiornato salvato: {output_dataset_file}")
        logger.info(f"📊 Esempi con immagini: {len(dataset_with_images)}")
        
        return output_dataset_file, success_count
    
    return None, 0

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Rasterizza tutti gli SVG del dataset T7")
    parser.add_argument('--dataset',
                       default='data/processed/xml_format_optimized/baseline_corrected_400_final.json',
                       help='File dataset corrente')
    parser.add_argument('--output_dir',
                       default='data/processed/xml_format_optimized/baseline_corrected_400_images',
                       help='Directory output immagini')
    parser.add_argument('--max_examples', type=int, default=None,
                       help='Numero massimo esempi da processare')
    parser.add_argument('--width', type=int, default=512,
                       help='Larghezza immagini output')
    parser.add_argument('--height', type=int, default=512,
                       help='Altezza immagini output')
    
    args = parser.parse_args()
    
    logger.info("🚀 RASTERIZZAZIONE COMPLETA DATASET T7")
    logger.info("=" * 50)
    logger.info(f"Dataset: {args.dataset}")
    logger.info(f"Output: {args.output_dir}")
    logger.info(f"Dimensioni: {args.width}x{args.height}")
    if args.max_examples:
        logger.info(f"Max esempi: {args.max_examples}")
    logger.info("=" * 50)
    
    # Verifica dipendenze
    if not check_dependencies():
        logger.error("❌ Dipendenze mancanti!")
        sys.exit(1)
    
    # Verifica dataset
    if not os.path.exists(args.dataset):
        logger.error(f"❌ Dataset non trovato: {args.dataset}")
        sys.exit(1)
    
    # Processa dataset
    output_dataset, success_count = process_dataset(
        args.dataset, 
        args.output_dir, 
        args.max_examples
    )
    
    if success_count > 0:
        logger.info(f"🎉 RASTERIZZAZIONE COMPLETATA!")
        logger.info(f"📊 {success_count} immagini create")
        logger.info(f"📁 Directory: {args.output_dir}")
        if output_dataset:
            logger.info(f"📄 Dataset aggiornato: {output_dataset}")
        
        logger.info("\n🎯 PROSSIMI PASSI:")
        logger.info("1. Verifica immagini generate")
        logger.info("2. Lancia baseline evaluation con dataset completo")
        logger.info("3. Calcola metriche BLEU-4, CIDEr, METEOR, CLIPScore")
    else:
        logger.error("❌ Nessuna immagine creata!")
        sys.exit(1)

if __name__ == "__main__":
    main()
