#!/usr/bin/env python3
import os, sys, json, argparse, logging, torch
from transformers import <PERSON>Token<PERSON>, AutoModelForCausalLM, TrainingArguments, Trainer, BitsAndBytesConfig, DataCollatorForLanguageModeling
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
import wandb

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SVGCaptionDataset:
    def __init__(self, data_file, tokenizer, max_length=2048):
        self.tokenizer = tokenizer
        self.max_length = max_length
        with open(data_file, 'r') as f:
            self.data = json.load(f)
        logger.info(f"Dataset caricato: {len(self.data)} esempi")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        xml = item['xml']
        caption = item['caption']
        
        if "gemma" in self.tokenizer.name_or_path.lower():
            prompt = f"<start_of_turn>user\nDescribe this SVG:\n{xml}<end_of_turn>\n<start_of_turn>model\n{caption}<end_of_turn>"
        elif "llama" in self.tokenizer.name_or_path.lower():
            prompt = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\nDescribe this SVG:\n{xml}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n{caption}<|eot_id|>"
        else:
            prompt = f"Describe this SVG:\n{xml}\n\nDescription: {caption}"
        
        encoding = self.tokenizer(prompt, truncation=True, max_length=self.max_length, padding=False, return_tensors="pt")
        return {"input_ids": encoding["input_ids"].flatten(), "attention_mask": encoding["attention_mask"].flatten(), "labels": encoding["input_ids"].flatten()}

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_name_or_path", type=str, required=True)
    parser.add_argument("--data_file", type=str, required=True)
    parser.add_argument("--config_path", type=str, required=True)
    parser.add_argument("--output_dir", type=str, required=True)
    parser.add_argument("--disable_quantization", action="store_true")
    parser.add_argument("--use_wandb", action="store_true")
    parser.add_argument("--wandb_project", type=str, default="svg_captioning")
    parser.add_argument("--wandb_run_name", type=str)
    parser.add_argument("--resume_from_checkpoint", type=str)
    args = parser.parse_args()
    
    with open(args.config_path, 'r') as f:
        config = json.load(f)
    
    if args.use_wandb:
        wandb.init(project=args.wandb_project, name=args.wandb_run_name, config=config)
    
    logger.info(f"Caricamento tokenizer: {args.model_name_or_path}")
    tokenizer = AutoTokenizer.from_pretrained(args.model_name_or_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    logger.info("Quantizzazione 4-bit FORZATA")
    quantization_config = BitsAndBytesConfig(load_in_4bit=True, bnb_4bit_compute_dtype=torch.float16, bnb_4bit_quant_type="nf4", bnb_4bit_use_double_quant=True)
    
    logger.info(f"Caricamento modello: {args.model_name_or_path}")
    model = AutoModelForCausalLM.from_pretrained(args.model_name_or_path, quantization_config=quantization_config, device_map="auto", torch_dtype=torch.float16, trust_remote_code=True)
    model = prepare_model_for_kbit_training(model)
    
    lora_config = LoraConfig(r=config.get("lora_r", 16), lora_alpha=config.get("lora_alpha", 32), target_modules=config.get("lora_target_modules", ["q_proj", "k_proj", "v_proj", "o_proj"]), lora_dropout=config.get("lora_dropout", 0.1), bias="none", task_type=TaskType.CAUSAL_LM)
    model = get_peft_model(model, lora_config)
    
    dataset = SVGCaptionDataset(args.data_file, tokenizer)
    
    training_args = TrainingArguments(output_dir=args.output_dir, per_device_train_batch_size=config.get("per_device_train_batch_size", 1), gradient_accumulation_steps=config.get("gradient_accumulation_steps", 8), learning_rate=config.get("learning_rate", 5e-5), num_train_epochs=config.get("num_train_epochs", 3), max_steps=config.get("max_steps", 45000), warmup_steps=config.get("warmup_steps", 500), logging_steps=config.get("logging_steps", 50), save_steps=config.get("save_steps", 250), fp16=True, dataloader_num_workers=2, remove_unused_columns=False, report_to="wandb" if args.use_wandb else None, run_name=args.wandb_run_name)
    
    data_collator = DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False)
    trainer = Trainer(model=model, args=training_args, train_dataset=dataset, data_collator=data_collator, tokenizer=tokenizer)
    
    resume_from_checkpoint = args.resume_from_checkpoint if args.resume_from_checkpoint and os.path.exists(args.resume_from_checkpoint) else None
    if resume_from_checkpoint:
        logger.info(f"Resume da checkpoint: {resume_from_checkpoint}")
    
    logger.info("Avvio training...")
    trainer.train(resume_from_checkpoint=resume_from_checkpoint)
    
    logger.info("Salvataggio modello...")
    trainer.save_model()
    tokenizer.save_pretrained(args.output_dir)
    logger.info("Training completato!")

if __name__ == "__main__":
    main()
