#!/usr/bin/env python3
"""
Script per pulizia completa e riorganizzazione del filesystem tesi_ediluzio
"""

import os
import shutil
import logging
from datetime import datetime
import json

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_directory_size(path):
    """Calcola dimensione directory"""
    total = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total += os.path.getsize(filepath)
    except:
        pass
    return total / (1024**3)  # GB

def main():
    print("🧹 PULIZIA COMPLETA FILESYSTEM TESI_EDILUZIO")
    print("=" * 60)
    
    # Spazio iniziale
    initial_size = get_directory_size(".")
    logger.info(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    
    # 1. SCRIPT OBSOLETI DA ELIMINARE
    obsolete_scripts = [
        # Script di lancio obsoleti
        "launch_baseline_jobs.sh",
        "launch_final_4_jobs.sh", 
        "launch_final_correct_setup.sh",
        "launch_fixed_final_jobs.sh",
        "launch_fixed_jobs.sh",
        "launch_gemma.sh",
        "launch_jobs.sh",
        "relaunch_missing_jobs.sh",
        
        # Script baseline obsoleti (mantieni solo FIXED)
        "run_gpt4v_REAL_colors.py",  # Non funziona senza API key
        
        # Script di check obsoleti
        "check_resume_status.py",
        "monitor_training.py",
        
        # File temporanei
        '"$OUTPUT_DIR"',  # Directory vuota con nome strano
        "tabella.md",  # File temporaneo
    ]
    
    logger.info("🗑️ Eliminazione script obsoleti...")
    for script in obsolete_scripts:
        if os.path.exists(script):
            if os.path.isdir(script):
                shutil.rmtree(script)
                logger.info(f"  🗑️ Directory eliminata: {script}")
            else:
                os.remove(script)
                logger.info(f"  🗑️ File eliminato: {script}")
    
    # 2. RIORGANIZZAZIONE SCRIPTS
    logger.info("📁 Riorganizzazione scripts...")
    
    # Crea directory organizzate
    os.makedirs("scripts/baseline", exist_ok=True)
    os.makedirs("scripts/cleanup", exist_ok=True)
    os.makedirs("scripts/reports", exist_ok=True)
    
    # Sposta script baseline
    baseline_scripts = [
        "run_blip2_REAL_colors.py",
        "run_florence2_REAL_colors.py", 
        "run_florence2_FIXED.py",
        "run_idefics3_REAL_colors.py",
        "run_idefics3_FIXED.py",
        "calculate_blip2_REAL_metrics.py"
    ]
    
    for script in baseline_scripts:
        if os.path.exists(script):
            shutil.move(script, f"scripts/baseline/{script}")
            logger.info(f"  📁 Spostato: {script} -> scripts/baseline/")
    
    # Sposta script di pulizia
    cleanup_scripts = [
        "clean_quota_safe.py",
        "clean_quota_emergency.py"
    ]
    
    for script in cleanup_scripts:
        if os.path.exists(script):
            shutil.move(script, f"scripts/cleanup/{script}")
            logger.info(f"  📁 Spostato: {script} -> scripts/cleanup/")
    
    # Sposta script di report
    report_scripts = [
        "generate_trained_models_report.py",
        "generate_report.sh",
        "generate_bleu_chart.py",
        "generate_radar_bleu_complete.py"
    ]
    
    for script in report_scripts:
        if os.path.exists(script):
            shutil.move(script, f"scripts/reports/{script}")
            logger.info(f"  📁 Spostato: {script} -> scripts/reports/")
    
    # 3. PULIZIA LOGS VECCHI
    logger.info("🧹 Pulizia logs vecchi...")
    
    # Mantieni solo log attivi
    active_jobs = ["2603363", "2603364", "2603369", "2603371"]  # Job attivi
    
    if os.path.exists("logs"):
        for filename in os.listdir("logs"):
            if filename.endswith((".out", ".err")):
                # Estrai job ID
                job_id = None
                for active in active_jobs:
                    if active in filename:
                        job_id = active
                        break
                
                if not job_id:
                    filepath = os.path.join("logs", filename)
                    os.remove(filepath)
                    logger.info(f"  🗑️ Log eliminato: {filename}")
    
    # 4. PULIZIA EVALUATION RESULTS DUPLICATI
    logger.info("🧹 Pulizia evaluation results duplicati...")
    
    if os.path.exists("evaluation_results"):
        # Mantieni solo i file più recenti per ogni modello
        files_to_keep = [
            "florence2_FIXED_results_20250713_102506.json",  # Più recente
            # Mantieni directory baseline
        ]
        
        for filename in os.listdir("evaluation_results"):
            filepath = os.path.join("evaluation_results", filename)
            if os.path.isfile(filepath) and filename not in files_to_keep:
                # Elimina file vecchi
                if any(model in filename for model in ["blip2", "florence2", "idefics3"]):
                    if "20250712" in filename:  # File di ieri
                        os.remove(filepath)
                        logger.info(f"  🗑️ Evaluation eliminato: {filename}")
    
    # 5. PULIZIA WANDB RUNS VECCHI
    logger.info("🧹 Pulizia WandB runs vecchi...")
    
    if os.path.exists("wandb"):
        # Mantieni solo run attivo
        current_run = "run-20250713_101823-gmxryhwm"  # GEMMA attivo
        
        for item in os.listdir("wandb"):
            if item.startswith("run-") and item != current_run:
                run_path = os.path.join("wandb", item)
                if os.path.isdir(run_path):
                    shutil.rmtree(run_path)
                    logger.info(f"  🗑️ WandB run eliminato: {item}")
    
    # 6. VERIFICA SPAZIO FINALE
    final_size = get_directory_size(".")
    space_freed = initial_size - final_size
    
    print("\n" + "=" * 60)
    print("🧹 PULIZIA FILESYSTEM COMPLETATA!")
    print("=" * 60)
    logger.info(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    logger.info(f"📊 Spazio finale: {final_size:.2f}GB")
    logger.info(f"🗑️ Spazio liberato: {space_freed:.2f}GB")
    
    # 7. RIEPILOGO ORGANIZZAZIONE
    print("\n📁 NUOVA ORGANIZZAZIONE:")
    print("✅ scripts/baseline/ - Script valutazione modelli baseline")
    print("✅ scripts/cleanup/ - Script pulizia quota")
    print("✅ scripts/reports/ - Script generazione report")
    print("✅ scripts/slurm/ - Job SLURM")
    print("✅ scripts/training/ - Script training")
    print("✅ logs/ - Solo log job attivi")
    print("✅ evaluation_results/ - Solo risultati recenti")
    print("✅ wandb/ - Solo run attivo")
    
    logger.info("🎉 Filesystem pulito e riorganizzato!")

if __name__ == "__main__":
    main()
