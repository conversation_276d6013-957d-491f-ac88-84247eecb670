#!/usr/bin/env python3
"""
Script di pulizia quota SICURO - Rispetta le regole di memoria sui checkpoint
Elimina solo cache sicure, mantiene checkpoint dei training attivi
"""

import os
import shutil
import glob
import logging
from datetime import datetime, timedelta
import subprocess

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# CONFIGURAZIONE SICUREZZA
PROTECTED_CHECKPOINT_DIRS = [
    "experiments/xml_direct_input/outputs/gemma_t9_no_accumulation",
    "experiments/xml_direct_input/outputs/llama_t8_24h", 
    "experiments/xml_direct_input/outputs/llama_t9_no_accumulation"
]

ACTIVE_JOB_IDS = ["2600849", "2600850", "2600851"]

def get_directory_size(path):
    """Calcola dimensione directory in GB"""
    try:
        result = subprocess.run(['du', '-sb', path], capture_output=True, text=True)
        if result.returncode == 0:
            bytes_size = int(result.stdout.split()[0])
            return bytes_size / (1024**3)  # Convert to GB
        return 0
    except:
        return 0

def clean_huggingface_cache():
    """Pulisce cache HuggingFace SICURA (solo file temporanei)"""
    logger.info("🧹 Pulizia cache HuggingFace...")
    
    cache_dir = "/work/tesi_ediluzio/.cache/huggingface"
    if not os.path.exists(cache_dir):
        logger.info("⚠️ Cache HuggingFace non trovata")
        return 0
    
    cleaned_size = 0
    
    # SICURO: Elimina solo file temporanei
    temp_patterns = [
        "**/*.tmp", "**/*temp*", "**/*.lock", "**/*.incomplete",
        "**/tmp_*", "**/.tmp*", "**/temp_*"
    ]
    
    for pattern in temp_patterns:
        temp_files = glob.glob(os.path.join(cache_dir, pattern), recursive=True)
        for temp_file in temp_files:
            try:
                if os.path.isfile(temp_file):
                    size = os.path.getsize(temp_file)
                    os.remove(temp_file)
                    cleaned_size += size
                    logger.info(f"🗑️ Eliminato temp file: {os.path.basename(temp_file)}")
                elif os.path.isdir(temp_file):
                    size = get_directory_size(temp_file) * (1024**3)
                    shutil.rmtree(temp_file)
                    cleaned_size += size
                    logger.info(f"🗑️ Eliminata temp dir: {os.path.basename(temp_file)}")
            except Exception as e:
                logger.warning(f"⚠️ Errore eliminazione {temp_file}: {e}")
    
    # SICURO: Elimina cache datasets (può essere rigenerata)
    datasets_cache = os.path.join(cache_dir, "datasets")
    if os.path.exists(datasets_cache):
        try:
            size = get_directory_size(datasets_cache) * (1024**3)
            shutil.rmtree(datasets_cache)
            cleaned_size += size
            logger.info(f"🗑️ Eliminata cache datasets: {size/(1024**3):.2f}GB")
        except Exception as e:
            logger.warning(f"⚠️ Errore eliminazione datasets cache: {e}")
    
    return cleaned_size

def clean_wandb_old_runs():
    """Pulisce run WandB vecchie (mantiene ultime 3)"""
    logger.info("🧹 Pulizia run WandB vecchie...")
    
    wandb_dir = "/work/tesi_ediluzio/wandb"
    if not os.path.exists(wandb_dir):
        logger.info("⚠️ Directory WandB non trovata")
        return 0
    
    cleaned_size = 0
    
    # Trova tutte le run
    run_dirs = glob.glob(os.path.join(wandb_dir, "run-*"))
    if len(run_dirs) <= 3:
        logger.info(f"✅ Solo {len(run_dirs)} run WandB, nessuna pulizia necessaria")
        return 0
    
    # Ordina per data di modifica (più vecchie prima)
    run_dirs.sort(key=lambda x: os.path.getmtime(x))
    
    # Elimina tutte tranne le ultime 3
    runs_to_delete = run_dirs[:-3]
    
    for run_dir in runs_to_delete:
        try:
            size = get_directory_size(run_dir) * (1024**3)
            shutil.rmtree(run_dir)
            cleaned_size += size
            logger.info(f"🗑️ Eliminata run vecchia: {os.path.basename(run_dir)}")
        except Exception as e:
            logger.warning(f"⚠️ Errore eliminazione run {run_dir}: {e}")
    
    return cleaned_size

def clean_logs_old():
    """Pulisce log vecchi (mantiene solo job attivi)"""
    logger.info("🧹 Pulizia log vecchi...")
    
    logs_dir = "/work/tesi_ediluzio/logs"
    if not os.path.exists(logs_dir):
        logger.info("⚠️ Directory logs non trovata")
        return 0
    
    cleaned_size = 0
    
    # Pattern per log da mantenere
    protected_patterns = []
    for job_id in ACTIVE_JOB_IDS:
        protected_patterns.extend([f"*{job_id}*", f"*FINAL*{job_id}*"])
    
    # Trova tutti i file log
    log_files = glob.glob(os.path.join(logs_dir, "*.out")) + glob.glob(os.path.join(logs_dir, "*.err"))
    
    for log_file in log_files:
        # Controlla se è protetto
        is_protected = False
        for pattern in protected_patterns:
            if any(job_id in os.path.basename(log_file) for job_id in ACTIVE_JOB_IDS):
                is_protected = True
                break
        
        if not is_protected:
            try:
                size = os.path.getsize(log_file)
                os.remove(log_file)
                cleaned_size += size
                logger.info(f"🗑️ Eliminato log vecchio: {os.path.basename(log_file)}")
            except Exception as e:
                logger.warning(f"⚠️ Errore eliminazione log {log_file}: {e}")
    
    return cleaned_size

def clean_home_cache():
    """Pulisce cache sicure in /homes/ediluzio"""
    logger.info("🧹 Pulizia cache /homes/ediluzio...")
    
    home_cache = "/homes/ediluzio/.cache"
    if not os.path.exists(home_cache):
        logger.info("⚠️ Cache home non trovata")
        return 0
    
    cleaned_size = 0
    
    # SICURO: Pulisce solo cache rigenerabili
    safe_caches = ["pip", "wandb"]
    
    for cache_name in safe_caches:
        cache_path = os.path.join(home_cache, cache_name)
        if os.path.exists(cache_path):
            try:
                size = get_directory_size(cache_path) * (1024**3)
                shutil.rmtree(cache_path)
                os.makedirs(cache_path, exist_ok=True)  # Ricrea directory vuota
                cleaned_size += size
                logger.info(f"🗑️ Pulita cache home: {cache_name}")
            except Exception as e:
                logger.warning(f"⚠️ Errore pulizia cache {cache_name}: {e}")
    
    return cleaned_size

def clean_python_cache():
    """Pulisce cache Python (__pycache__)"""
    logger.info("🧹 Pulizia cache Python...")
    
    cleaned_size = 0
    
    # Trova tutte le directory __pycache__
    pycache_dirs = []
    for root, dirs, files in os.walk("/work/tesi_ediluzio"):
        if "__pycache__" in dirs:
            pycache_dirs.append(os.path.join(root, "__pycache__"))
    
    for pycache_dir in pycache_dirs:
        try:
            size = get_directory_size(pycache_dir) * (1024**3)
            shutil.rmtree(pycache_dir)
            cleaned_size += size
            logger.info(f"🗑️ Eliminata cache Python: {pycache_dir}")
        except Exception as e:
            logger.warning(f"⚠️ Errore eliminazione cache Python {pycache_dir}: {e}")
    
    return cleaned_size

def verify_protected_checkpoints():
    """Verifica che i checkpoint protetti esistano ancora"""
    logger.info("🛡️ Verifica checkpoint protetti...")
    
    for checkpoint_dir in PROTECTED_CHECKPOINT_DIRS:
        full_path = f"/work/tesi_ediluzio/{checkpoint_dir}"
        if os.path.exists(full_path):
            checkpoints = glob.glob(os.path.join(full_path, "checkpoint-*"))
            logger.info(f"✅ {os.path.basename(checkpoint_dir)}: {len(checkpoints)} checkpoint protetti")
        else:
            logger.warning(f"⚠️ Directory checkpoint non trovata: {checkpoint_dir}")

def main():
    print("🧹 PULIZIA QUOTA SICURA")
    print("=" * 50)
    print("🛡️ REGOLE DI SICUREZZA:")
    print("✅ MANTIENE: Checkpoint training attivi")
    print("✅ MANTIENE: Modelli HuggingFace necessari")
    print("✅ MANTIENE: Log job attivi")
    print("❌ ELIMINA: Solo cache temporanee e rigenerabili")
    print("=" * 50)
    
    # Spazio iniziale
    initial_size = get_directory_size("/work/tesi_ediluzio")
    logger.info(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    
    # Verifica checkpoint protetti
    verify_protected_checkpoints()
    
    total_cleaned = 0
    
    # Pulizie sicure
    total_cleaned += clean_huggingface_cache()
    total_cleaned += clean_wandb_old_runs()
    total_cleaned += clean_logs_old()
    total_cleaned += clean_home_cache()
    total_cleaned += clean_python_cache()
    
    # Spazio finale
    final_size = get_directory_size("/work/tesi_ediluzio")
    actual_freed = (initial_size - final_size) * (1024**3)
    
    print("\n" + "=" * 50)
    print("🎉 PULIZIA COMPLETATA!")
    print("=" * 50)
    print(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    print(f"📊 Spazio finale: {final_size:.2f}GB")
    print(f"🗑️ Spazio liberato: {actual_freed/(1024**3):.2f}GB")
    print(f"✅ Checkpoint protetti: INTATTI")
    print(f"✅ Training attivi: NON DISTURBATI")
    print("=" * 50)
    
    # Verifica finale
    verify_protected_checkpoints()

if __name__ == "__main__":
    main()
