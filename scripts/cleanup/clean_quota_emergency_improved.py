#!/usr/bin/env python3
"""
Script di pulizia quota EMERGENZA MIGLIORATO - Versione dinamica e aggressiva
Per quando la quota è VERAMENTE piena - Mantiene sempre ultimi 2 checkpoint
"""

import os
import shutil
import glob
import logging
import subprocess
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_directory_size(path):
    """Calcola dimensione directory in GB"""
    try:
        result = subprocess.run(['du', '-sb', path], capture_output=True, text=True)
        if result.returncode == 0:
            bytes_size = int(result.stdout.split()[0])
            return bytes_size / (1024**3)
        return 0
    except:
        return 0

def get_active_jobs():
    """Rileva automaticamente job SLURM attivi"""
    try:
        result = subprocess.run(['squeue', '-u', 'ediluzio', '--format=%i,%j,%T'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]
            active_jobs = []
            for line in lines:
                if line.strip():
                    job_id, job_name, status = line.split(',')
                    if status in ['RUNNING', 'PENDING']:
                        active_jobs.append(job_id.strip())
            return active_jobs
        return []
    except:
        return []

def find_checkpoint_directories():
    """Trova automaticamente directory con checkpoint"""
    checkpoint_dirs = []
    outputs_dir = "/work/tesi_ediluzio/experiments/xml_direct_input/outputs"
    
    if os.path.exists(outputs_dir):
        for model_dir in os.listdir(outputs_dir):
            model_path = os.path.join(outputs_dir, model_dir)
            if os.path.isdir(model_path):
                checkpoints = glob.glob(os.path.join(model_path, "checkpoint-*"))
                if checkpoints:
                    checkpoint_dirs.append(model_path)
    
    return checkpoint_dirs

def emergency_clean_old_checkpoints():
    """EMERGENZA: Mantiene SOLO ultimi 2 checkpoint per modello"""
    logger.info("🚨 EMERGENZA: Pulizia checkpoint (mantiene ultimi 2)...")
    
    checkpoint_dirs = find_checkpoint_directories()
    cleaned_size = 0
    
    for checkpoint_dir in checkpoint_dirs:
        checkpoints = glob.glob(os.path.join(checkpoint_dir, "checkpoint-*"))
        
        if len(checkpoints) <= 2:
            logger.info(f"✅ {os.path.basename(checkpoint_dir)}: Solo {len(checkpoints)} checkpoint")
            continue
        
        # Ordina per numero di step
        def extract_step(path):
            try:
                return int(os.path.basename(path).split('-')[1])
            except:
                return 0
        
        checkpoints.sort(key=extract_step)
        checkpoints_to_delete = checkpoints[:-2]  # Mantiene ultimi 2
        
        for checkpoint in checkpoints_to_delete:
            try:
                size = get_directory_size(checkpoint) * (1024**3)
                shutil.rmtree(checkpoint)
                cleaned_size += size
                step = os.path.basename(checkpoint)
                logger.info(f"🗑️ EMERGENZA: {os.path.basename(checkpoint_dir)}/{step} ({size/(1024**3):.1f}GB)")
            except Exception as e:
                logger.warning(f"⚠️ Errore: {e}")
    
    return cleaned_size

def emergency_clean_huggingface_aggressive():
    """EMERGENZA: Pulizia aggressiva cache HuggingFace"""
    logger.info("🚨 EMERGENZA: Pulizia aggressiva HuggingFace...")
    
    cache_dir = "/work/tesi_ediluzio/.cache/huggingface"
    if not os.path.exists(cache_dir):
        return 0
    
    cleaned_size = 0
    
    # AGGRESSIVO: Elimina TUTTA la cache transformers (può essere riscaricata)
    transformers_cache = os.path.join(cache_dir, "transformers")
    if os.path.exists(transformers_cache):
        try:
            size = get_directory_size(transformers_cache) * (1024**3)
            shutil.rmtree(transformers_cache)
            os.makedirs(transformers_cache, exist_ok=True)
            cleaned_size += size
            logger.info(f"🗑️ EMERGENZA: Cache transformers eliminata ({size/(1024**3):.1f}GB)")
        except Exception as e:
            logger.warning(f"⚠️ Errore cache transformers: {e}")
    
    # AGGRESSIVO: Mantiene solo modelli Gemma e Llama più recenti
    hub_cache = os.path.join(cache_dir, "hub")
    if os.path.exists(hub_cache):
        cutoff_time = datetime.now() - timedelta(days=1)  # Solo ultimo giorno
        
        for model_dir in os.listdir(hub_cache):
            model_path = os.path.join(hub_cache, model_dir)
            if os.path.isdir(model_path):
                try:
                    last_access = datetime.fromtimestamp(os.path.getmtime(model_path))
                    
                    # Mantiene SOLO Gemma/Llama recenti
                    if ("gemma" in model_dir.lower() or "llama" in model_dir.lower()) and last_access > cutoff_time:
                        logger.info(f"✅ Mantenuto: {model_dir}")
                        continue
                    
                    # Elimina tutto il resto
                    size = get_directory_size(model_path) * (1024**3)
                    shutil.rmtree(model_path)
                    cleaned_size += size
                    logger.info(f"🗑️ EMERGENZA: {model_dir} ({size/(1024**3):.1f}GB)")
                    
                except Exception as e:
                    logger.warning(f"⚠️ Errore modello {model_dir}: {e}")
    
    return cleaned_size

def emergency_clean_all_logs():
    """EMERGENZA: Elimina TUTTI i log tranne job attivi"""
    logger.info("🚨 EMERGENZA: Pulizia massiva log...")
    
    logs_dir = "/work/tesi_ediluzio/logs"
    if not os.path.exists(logs_dir):
        return 0
    
    active_jobs = get_active_jobs()
    cleaned_size = 0
    
    log_files = glob.glob(os.path.join(logs_dir, "*.out")) + glob.glob(os.path.join(logs_dir, "*.err"))
    
    for log_file in log_files:
        try:
            # Mantiene SOLO log di job attivi
            is_active = any(job_id in os.path.basename(log_file) for job_id in active_jobs)
            
            if not is_active:
                size = os.path.getsize(log_file)
                os.remove(log_file)
                cleaned_size += size
                logger.info(f"🗑️ EMERGENZA: {os.path.basename(log_file)}")
            else:
                logger.info(f"✅ Protetto: {os.path.basename(log_file)}")
                
        except Exception as e:
            logger.warning(f"⚠️ Errore log {log_file}: {e}")
    
    return cleaned_size

def emergency_clean_wandb_minimal():
    """EMERGENZA: Mantiene solo ultima run WandB"""
    logger.info("🚨 EMERGENZA: Pulizia massiva WandB...")
    
    wandb_dir = "/work/tesi_ediluzio/wandb"
    if not os.path.exists(wandb_dir):
        return 0
    
    cleaned_size = 0
    run_dirs = glob.glob(os.path.join(wandb_dir, "run-*"))
    
    if len(run_dirs) <= 1:
        return 0
    
    # Ordina per data e mantiene solo l'ultima
    run_dirs.sort(key=lambda x: os.path.getmtime(x))
    runs_to_delete = run_dirs[:-1]
    
    for run_dir in runs_to_delete:
        try:
            size = get_directory_size(run_dir) * (1024**3)
            shutil.rmtree(run_dir)
            cleaned_size += size
            logger.info(f"🗑️ EMERGENZA: {os.path.basename(run_dir)} ({size/(1024**3):.1f}GB)")
        except Exception as e:
            logger.warning(f"⚠️ Errore run {run_dir}: {e}")
    
    return cleaned_size

def emergency_clean_temp_files():
    """EMERGENZA: Elimina tutti i file temporanei"""
    logger.info("🚨 EMERGENZA: Pulizia file temporanei...")
    
    cleaned_size = 0
    temp_patterns = [
        "/work/tesi_ediluzio/**/*.tmp",
        "/work/tesi_ediluzio/**/*temp*",
        "/work/tesi_ediluzio/**/.tmp*",
        "/work/tesi_ediluzio/**/tmp_*",
        "/work/tesi_ediluzio/**/*.lock",
        "/work/tesi_ediluzio/**/*.pid"
    ]
    
    for pattern in temp_patterns:
        temp_files = glob.glob(pattern, recursive=True)
        for temp_file in temp_files:
            try:
                if os.path.isfile(temp_file):
                    size = os.path.getsize(temp_file)
                    os.remove(temp_file)
                    cleaned_size += size
                elif os.path.isdir(temp_file):
                    size = get_directory_size(temp_file) * (1024**3)
                    shutil.rmtree(temp_file)
                    cleaned_size += size
            except Exception as e:
                continue
    
    return cleaned_size

def verify_critical_survival():
    """Verifica che i file critici siano sopravvissuti"""
    logger.info("🛡️ Verifica sopravvivenza file critici...")
    
    critical_files = [
        "scripts/training/train_lora_multi_gpu_simple.py",
        "data/processed/xml_format_optimized/train_set_corrected_90k.json",
        "experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation_FIXED.json",
        "experiments/xml_direct_input/configs/llama_t8_final_optimized.json"
    ]
    
    all_ok = True
    for file_path in critical_files:
        full_path = f"/work/tesi_ediluzio/{file_path}"
        if os.path.exists(full_path):
            logger.info(f"✅ SOPRAVVISSUTO: {os.path.basename(file_path)}")
        else:
            logger.error(f"❌ PERSO: {file_path}")
            all_ok = False
    
    # Verifica checkpoint
    checkpoint_dirs = find_checkpoint_directories()
    for checkpoint_dir in checkpoint_dirs:
        checkpoints = glob.glob(os.path.join(checkpoint_dir, "checkpoint-*"))
        if len(checkpoints) >= 1:
            logger.info(f"✅ CHECKPOINT OK: {os.path.basename(checkpoint_dir)} ({len(checkpoints)} checkpoint)")
        else:
            logger.error(f"❌ CHECKPOINT PERSI: {checkpoint_dir}")
            all_ok = False
    
    return all_ok

def main():
    print("🚨 PULIZIA QUOTA EMERGENZA MIGLIORATA")
    print("=" * 60)
    print("⚠️ ATTENZIONE: Pulizia MOLTO aggressiva!")
    print("🛡️ GARANZIE EMERGENZA:")
    print("✅ MANTIENE: Ultimi 2 checkpoint per modello")
    print("✅ MANTIENE: File critici training")
    print("✅ MANTIENE: Log job attivi")
    print("✅ MANTIENE: Solo modelli Gemma/Llama recenti")
    print("❌ ELIMINA: Tutto il resto!")
    print("=" * 60)
    
    # Conferma tripla
    print("\n🚨 QUESTA È UNA PULIZIA EMERGENZA!")
    print("Eliminerà AGGRESSIVAMENTE cache, log, run WandB vecchie")
    response = input("Continuare? (digita 'EMERGENZA' per confermare): ")
    if response != "EMERGENZA":
        print("❌ Pulizia annullata")
        return
    
    # Rileva sistema
    active_jobs = get_active_jobs()
    checkpoint_dirs = find_checkpoint_directories()
    
    print(f"\n🔍 Sistema rilevato:")
    print(f"   Job attivi: {len(active_jobs)}")
    print(f"   Directory checkpoint: {len(checkpoint_dirs)}")
    
    # Spazio iniziale
    initial_size = get_directory_size("/work/tesi_ediluzio")
    logger.info(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    
    total_cleaned = 0
    
    # Pulizie emergenza
    total_cleaned += emergency_clean_old_checkpoints()
    total_cleaned += emergency_clean_huggingface_aggressive()
    total_cleaned += emergency_clean_all_logs()
    total_cleaned += emergency_clean_wandb_minimal()
    total_cleaned += emergency_clean_temp_files()
    
    # Pulizie standard
    try:
        from clean_quota_safe_improved import clean_home_cache, clean_python_cache
        total_cleaned += clean_home_cache()
        total_cleaned += clean_python_cache()
    except:
        pass
    
    # Spazio finale
    final_size = get_directory_size("/work/tesi_ediluzio")
    actual_freed = (initial_size - final_size) * (1024**3)
    
    print("\n" + "=" * 60)
    print("🚨 PULIZIA EMERGENZA COMPLETATA!")
    print("=" * 60)
    print(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    print(f"📊 Spazio finale: {final_size:.2f}GB")
    print(f"🗑️ Spazio liberato: {actual_freed/(1024**3):.2f}GB")
    print("=" * 60)
    
    # Verifica critica
    if verify_critical_survival():
        print("✅ TUTTI I FILE CRITICI SONO SOPRAVVISSUTI!")
    else:
        print("❌ ALCUNI FILE CRITICI SONO STATI PERSI!")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
