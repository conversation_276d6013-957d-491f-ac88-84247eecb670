#!/usr/bin/env python3
"""
Pulizia script SLURM obsoleti
"""

import os
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    print("🧹 PULIZIA SCRIPT SLURM OBSOLETI")
    print("=" * 40)
    
    # Script SLURM da mantenere (attivi/utili)
    scripts_to_keep = [
        # Job attivi
        "GEMMA_T9_RESUME_2250.slurm",
        "LLAMA_T8_RESUME_4500.slurm", 
        "BASELINE_FIXED.slurm",
        "DEEPSPEED_AUTH_TEST.slurm",
        
        # Baseline funzionanti
        "BASELINE_BLIP2_400_LEONARDO.slurm",
        "BASELINE_FLORENCE2_400_LEONARDO.slurm",
        "BASELINE_IDEFICS3_400_LEONARDO.slurm",
        
        # Template utili
        "LLAMA_T8_DEEPSPEED_AUTH.slurm",
        "GEMMA_T9_DEEPSPEED_LEONARDO.slurm",
        "LLAMA_T8_DEEPSPEED_LEONARDO.slurm",
        
        # Report
        "generate_final_report.slurm"
    ]
    
    slurm_dir = "scripts/slurm"
    if not os.path.exists(slurm_dir):
        logger.info("❌ Directory scripts/slurm non trovata")
        return
    
    # Elimina script obsoleti
    for filename in os.listdir(slurm_dir):
        if filename.endswith(".slurm") and filename not in scripts_to_keep:
            filepath = os.path.join(slurm_dir, filename)
            os.remove(filepath)
            logger.info(f"🗑️ Eliminato: {filename}")
    
    # Elimina anche script .sh obsoleti
    for filename in os.listdir(slurm_dir):
        if filename.endswith(".sh"):
            filepath = os.path.join(slurm_dir, filename)
            os.remove(filepath)
            logger.info(f"🗑️ Eliminato: {filename}")
    
    print("\n✅ SCRIPT SLURM MANTENUTI:")
    for script in sorted(scripts_to_keep):
        if os.path.exists(os.path.join(slurm_dir, script)):
            print(f"  ✅ {script}")
    
    logger.info("🎉 Pulizia script SLURM completata!")

if __name__ == "__main__":
    main()
