#!/usr/bin/env python3
"""
Pulizia aggressiva cache HuggingFace
"""

import os
import shutil
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_size_gb(path):
    """Calcola dimensione in GB"""
    total = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total += os.path.getsize(filepath)
    except:
        pass
    return total / (1024**3)

def main():
    print("🧹 PULIZIA AGGRESSIVA CACHE HUGGINGFACE")
    print("=" * 50)
    
    # Spazio iniziale
    initial_size = get_size_gb(".")
    logger.info(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    
    # Pulizia cache HuggingFace
    cache_paths = [
        ".cache/huggingface/hub",
        ".cache/huggingface/datasets", 
        ".cache/huggingface/transformers",
        ".cache/huggingface/modules"
    ]
    
    for cache_path in cache_paths:
        if os.path.exists(cache_path):
            cache_size = get_size_gb(cache_path)
            logger.info(f"🗑️ Eliminando {cache_path}: {cache_size:.2f}GB")
            shutil.rmtree(cache_path)
            os.makedirs(cache_path, exist_ok=True)
    
    # Pulizia chunks (mantieni solo alcuni)
    if os.path.exists("chunks"):
        chunk_size = get_size_gb("chunks")
        logger.info(f"📊 Chunks attuali: {chunk_size:.2f}GB")
        
        # Mantieni solo primi 10 chunks per test
        chunks_to_keep = [f"chunk{i:02d}.json" for i in range(10)]
        
        for filename in os.listdir("chunks"):
            if filename.endswith(".json") and filename not in chunks_to_keep:
                filepath = os.path.join("chunks", filename)
                os.remove(filepath)
                logger.info(f"  🗑️ Chunk eliminato: {filename}")
    
    # Spazio finale
    final_size = get_size_gb(".")
    space_freed = initial_size - final_size
    
    print("\n" + "=" * 50)
    print("🧹 PULIZIA AGGRESSIVA COMPLETATA!")
    print("=" * 50)
    logger.info(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    logger.info(f"📊 Spazio finale: {final_size:.2f}GB")
    logger.info(f"🗑️ Spazio liberato: {space_freed:.2f}GB")

if __name__ == "__main__":
    main()
