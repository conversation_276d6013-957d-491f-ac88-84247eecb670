#!/usr/bin/env python3
"""
Script di pulizia quota SICURO MIGLIORATO - Versione dinamica e intelligente
Rileva automaticamente job attivi e checkpoint correnti
"""

import os
import shutil
import glob
import logging
import subprocess
import json
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_directory_size(path):
    """Calcola dimensione directory in GB"""
    try:
        result = subprocess.run(['du', '-sb', path], capture_output=True, text=True)
        if result.returncode == 0:
            bytes_size = int(result.stdout.split()[0])
            return bytes_size / (1024**3)
        return 0
    except:
        return 0

def get_active_jobs():
    """Rileva automaticamente job SLURM attivi dell'utente"""
    try:
        result = subprocess.run(['squeue', '-u', 'ediluzio', '--format=%i,%j,%T'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            active_jobs = []
            for line in lines:
                if line.strip():
                    job_id, job_name, status = line.split(',')
                    if status in ['RUNNING', 'PENDING']:
                        active_jobs.append(job_id.strip())
            logger.info(f"🔍 Job attivi rilevati: {active_jobs}")
            return active_jobs
        return []
    except:
        logger.warning("⚠️ Impossibile rilevare job attivi")
        return []

def find_checkpoint_directories():
    """Trova automaticamente directory con checkpoint attivi"""
    checkpoint_dirs = []
    
    # Cerca in experiments/xml_direct_input/outputs
    outputs_dir = "/work/tesi_ediluzio/experiments/xml_direct_input/outputs"
    if os.path.exists(outputs_dir):
        for model_dir in os.listdir(outputs_dir):
            model_path = os.path.join(outputs_dir, model_dir)
            if os.path.isdir(model_path):
                # Controlla se ha checkpoint
                checkpoints = glob.glob(os.path.join(model_path, "checkpoint-*"))
                if checkpoints:
                    rel_path = f"experiments/xml_direct_input/outputs/{model_dir}"
                    checkpoint_dirs.append(rel_path)
                    logger.info(f"🔍 Checkpoint trovati: {rel_path} ({len(checkpoints)} checkpoint)")
    
    return checkpoint_dirs

def clean_huggingface_cache_smart():
    """Pulizia intelligente cache HuggingFace - Mantiene modelli recenti"""
    logger.info("🧹 Pulizia intelligente cache HuggingFace...")
    
    cache_dir = "/work/tesi_ediluzio/.cache/huggingface"
    if not os.path.exists(cache_dir):
        logger.info("⚠️ Cache HuggingFace non trovata")
        return 0
    
    cleaned_size = 0
    
    # 1. SICURO: Elimina file temporanei
    temp_patterns = [
        "**/*.tmp", "**/*temp*", "**/*.lock", "**/*.incomplete",
        "**/tmp_*", "**/.tmp*", "**/temp_*", "**/*.downloading"
    ]
    
    for pattern in temp_patterns:
        temp_files = glob.glob(os.path.join(cache_dir, pattern), recursive=True)
        for temp_file in temp_files:
            try:
                if os.path.isfile(temp_file):
                    size = os.path.getsize(temp_file)
                    os.remove(temp_file)
                    cleaned_size += size
                elif os.path.isdir(temp_file):
                    size = get_directory_size(temp_file) * (1024**3)
                    shutil.rmtree(temp_file)
                    cleaned_size += size
            except Exception as e:
                logger.warning(f"⚠️ Errore eliminazione {temp_file}: {e}")
    
    # 2. SICURO: Elimina cache datasets
    datasets_cache = os.path.join(cache_dir, "datasets")
    if os.path.exists(datasets_cache):
        try:
            size = get_directory_size(datasets_cache) * (1024**3)
            shutil.rmtree(datasets_cache)
            cleaned_size += size
            logger.info(f"🗑️ Cache datasets eliminata: {size/(1024**3):.2f}GB")
        except Exception as e:
            logger.warning(f"⚠️ Errore eliminazione datasets cache: {e}")
    
    # 3. INTELLIGENTE: Mantiene solo modelli usati di recente (ultimi 7 giorni)
    hub_cache = os.path.join(cache_dir, "hub")
    if os.path.exists(hub_cache):
        cutoff_time = datetime.now() - timedelta(days=7)
        
        for model_dir in os.listdir(hub_cache):
            model_path = os.path.join(hub_cache, model_dir)
            if os.path.isdir(model_path):
                try:
                    # Controlla data ultimo accesso
                    last_access = datetime.fromtimestamp(os.path.getmtime(model_path))
                    
                    # Mantiene modelli Gemma e Llama recenti
                    if ("gemma" in model_dir.lower() or "llama" in model_dir.lower()) and last_access > cutoff_time:
                        logger.info(f"✅ Mantenuto modello recente: {model_dir}")
                        continue
                    
                    # Elimina modelli vecchi o non utilizzati
                    if last_access < cutoff_time:
                        size = get_directory_size(model_path) * (1024**3)
                        shutil.rmtree(model_path)
                        cleaned_size += size
                        logger.info(f"🗑️ Eliminato modello vecchio: {model_dir} ({size/(1024**3):.2f}GB)")
                        
                except Exception as e:
                    logger.warning(f"⚠️ Errore controllo modello {model_dir}: {e}")
    
    return cleaned_size

def clean_logs_smart(active_jobs):
    """Pulizia intelligente log - Mantiene solo job attivi e recenti"""
    logger.info("🧹 Pulizia intelligente log...")
    
    logs_dir = "/work/tesi_ediluzio/logs"
    if not os.path.exists(logs_dir):
        return 0
    
    cleaned_size = 0
    cutoff_time = datetime.now() - timedelta(hours=24)  # Mantiene log ultime 24h
    
    log_files = glob.glob(os.path.join(logs_dir, "*.out")) + glob.glob(os.path.join(logs_dir, "*.err"))
    
    for log_file in log_files:
        try:
            # Controlla se è di un job attivo
            is_active_job = any(job_id in os.path.basename(log_file) for job_id in active_jobs)
            
            # Controlla se è recente
            last_modified = datetime.fromtimestamp(os.path.getmtime(log_file))
            is_recent = last_modified > cutoff_time
            
            # Mantiene log di job attivi o recenti
            if is_active_job or is_recent:
                logger.info(f"✅ Mantenuto log: {os.path.basename(log_file)}")
                continue
            
            # Elimina log vecchi
            size = os.path.getsize(log_file)
            os.remove(log_file)
            cleaned_size += size
            logger.info(f"🗑️ Eliminato log vecchio: {os.path.basename(log_file)}")
            
        except Exception as e:
            logger.warning(f"⚠️ Errore eliminazione log {log_file}: {e}")
    
    return cleaned_size

def clean_wandb_smart():
    """Pulizia intelligente WandB - Mantiene run recenti e importanti"""
    logger.info("🧹 Pulizia intelligente WandB...")
    
    wandb_dir = "/work/tesi_ediluzio/wandb"
    if not os.path.exists(wandb_dir):
        return 0
    
    cleaned_size = 0
    cutoff_time = datetime.now() - timedelta(days=3)  # Mantiene run ultime 3 giorni
    
    run_dirs = glob.glob(os.path.join(wandb_dir, "run-*"))
    if len(run_dirs) <= 5:  # Mantiene sempre almeno 5 run
        logger.info(f"✅ Solo {len(run_dirs)} run WandB, nessuna pulizia")
        return 0
    
    # Ordina per data di modifica
    run_dirs.sort(key=lambda x: os.path.getmtime(x))
    
    # Mantiene ultime 5 run + run recenti
    runs_to_keep = run_dirs[-5:]  # Ultime 5
    
    for run_dir in run_dirs[:-5]:  # Controlla le altre
        try:
            last_modified = datetime.fromtimestamp(os.path.getmtime(run_dir))
            
            if last_modified > cutoff_time:
                runs_to_keep.append(run_dir)
                logger.info(f"✅ Mantenuta run recente: {os.path.basename(run_dir)}")
            else:
                size = get_directory_size(run_dir) * (1024**3)
                shutil.rmtree(run_dir)
                cleaned_size += size
                logger.info(f"🗑️ Eliminata run vecchia: {os.path.basename(run_dir)}")
                
        except Exception as e:
            logger.warning(f"⚠️ Errore eliminazione run {run_dir}: {e}")
    
    return cleaned_size

def verify_system_health(checkpoint_dirs):
    """Verifica salute del sistema dopo pulizia"""
    logger.info("🛡️ Verifica salute sistema...")
    
    # Verifica checkpoint
    for checkpoint_dir in checkpoint_dirs:
        full_path = f"/work/tesi_ediluzio/{checkpoint_dir}"
        if os.path.exists(full_path):
            checkpoints = glob.glob(os.path.join(full_path, "checkpoint-*"))
            logger.info(f"✅ {os.path.basename(checkpoint_dir)}: {len(checkpoints)} checkpoint OK")
        else:
            logger.warning(f"⚠️ Directory checkpoint mancante: {checkpoint_dir}")
    
    # Verifica file critici
    critical_files = [
        "scripts/training/train_lora_multi_gpu_simple.py",
        "data/processed/xml_format_optimized/train_set_corrected_90k.json",
        "experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation_FIXED.json",
        "experiments/xml_direct_input/configs/llama_t8_final_optimized.json"
    ]
    
    for file_path in critical_files:
        full_path = f"/work/tesi_ediluzio/{file_path}"
        if os.path.exists(full_path):
            logger.info(f"✅ File critico OK: {os.path.basename(file_path)}")
        else:
            logger.error(f"❌ File critico MANCANTE: {file_path}")

def main():
    print("🧹 PULIZIA QUOTA SICURA MIGLIORATA")
    print("=" * 60)
    print("🤖 FUNZIONALITÀ INTELLIGENTI:")
    print("✅ Rileva automaticamente job attivi")
    print("✅ Trova automaticamente checkpoint correnti")
    print("✅ Mantiene modelli HF usati di recente")
    print("✅ Pulizia temporizzata (log 24h, wandb 3gg)")
    print("❌ Elimina solo cache sicure e file vecchi")
    print("=" * 60)
    
    # Rileva sistema attuale
    active_jobs = get_active_jobs()
    checkpoint_dirs = find_checkpoint_directories()
    
    # Spazio iniziale
    initial_size = get_directory_size("/work/tesi_ediluzio")
    logger.info(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    
    total_cleaned = 0
    
    # Pulizie intelligenti
    total_cleaned += clean_huggingface_cache_smart()
    total_cleaned += clean_logs_smart(active_jobs)
    total_cleaned += clean_wandb_smart()
    
    # Pulizie standard sicure
    from clean_quota_safe import clean_home_cache, clean_python_cache
    total_cleaned += clean_home_cache()
    total_cleaned += clean_python_cache()
    
    # Spazio finale
    final_size = get_directory_size("/work/tesi_ediluzio")
    actual_freed = (initial_size - final_size) * (1024**3)
    
    print("\n" + "=" * 60)
    print("🎉 PULIZIA INTELLIGENTE COMPLETATA!")
    print("=" * 60)
    print(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    print(f"📊 Spazio finale: {final_size:.2f}GB")
    print(f"🗑️ Spazio liberato: {actual_freed/(1024**3):.2f}GB")
    print(f"🤖 Job attivi protetti: {len(active_jobs)}")
    print(f"✅ Checkpoint protetti: {len(checkpoint_dirs)} directory")
    print("=" * 60)
    
    # Verifica finale
    verify_system_health(checkpoint_dirs)

if __name__ == "__main__":
    main()
