#!/usr/bin/env python3
"""
Monitor automatico quota disco - Esegue pulizia automatica quando necessario
"""

import os
import subprocess
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_disk_usage():
    """Ottiene utilizzo disco corrente"""
    try:
        result = subprocess.run(['du', '-sb', '/work/tesi_ediluzio'], capture_output=True, text=True)
        if result.returncode == 0:
            bytes_used = int(result.stdout.split()[0])
            gb_used = bytes_used / (1024**3)
            return gb_used
        return 0
    except:
        return 0

def check_quota_status():
    """Controlla stato quota e determina azione necessaria"""
    current_usage = get_disk_usage()
    
    # Soglie (assumendo quota ~100GB)
    QUOTA_LIMIT = 95.0  # GB
    WARNING_THRESHOLD = 80.0  # GB
    EMERGENCY_THRESHOLD = 90.0  # GB
    
    logger.info(f"📊 Utilizzo disco corrente: {current_usage:.2f}GB")
    
    if current_usage >= EMERGENCY_THRESHOLD:
        return "EMERGENCY", current_usage
    elif current_usage >= WARNING_THRESHOLD:
        return "WARNING", current_usage
    else:
        return "OK", current_usage

def run_safe_cleanup():
    """Esegue pulizia sicura"""
    logger.info("🧹 Esecuzione pulizia sicura...")
    try:
        result = subprocess.run(['python', 'scripts/cleanup/clean_quota_safe_improved.py'], 
                              capture_output=True, text=True, cwd='/work/tesi_ediluzio')
        return result.returncode == 0
    except:
        return False

def main():
    print("🔍 MONITOR QUOTA DISCO")
    print("=" * 40)
    
    status, usage = check_quota_status()
    
    if status == "OK":
        print(f"✅ Quota OK: {usage:.2f}GB")
        print("Nessuna azione necessaria")
        
    elif status == "WARNING":
        print(f"⚠️ Quota in WARNING: {usage:.2f}GB")
        print("Esecuzione pulizia sicura...")
        
        if run_safe_cleanup():
            new_usage = get_disk_usage()
            freed = usage - new_usage
            print(f"✅ Pulizia completata!")
            print(f"📊 Spazio liberato: {freed:.2f}GB")
            print(f"📊 Nuovo utilizzo: {new_usage:.2f}GB")
        else:
            print("❌ Pulizia fallita!")
            
    elif status == "EMERGENCY":
        print(f"🚨 QUOTA EMERGENZA: {usage:.2f}GB")
        print("⚠️ Eseguire manualmente:")
        print("   python scripts/cleanup/clean_quota_emergency_improved.py")
        print("🚨 ATTENZIONE: Pulizia aggressiva richiesta!")
    
    print("=" * 40)

if __name__ == "__main__":
    main()
