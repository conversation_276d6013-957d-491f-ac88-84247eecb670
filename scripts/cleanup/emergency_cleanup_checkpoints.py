#!/usr/bin/env python3
"""
PULIZIA URGENTE CHECKPOINT - Mantieni solo ultimi 2 per job attivo
"""

import os
import shutil
import logging
import re
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_size_gb(path):
    """Calcola dimensione in GB"""
    total = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total += os.path.getsize(filepath)
    except:
        pass
    return total / (1024**3)

def get_checkpoint_number(checkpoint_name):
    """Estrae numero da checkpoint-XXXX"""
    match = re.search(r'checkpoint-(\d+)', checkpoint_name)
    return int(match.group(1)) if match else 0

def cleanup_experiment_checkpoints(exp_dir, keep_last=2):
    """Pulisce checkpoint mantenendo solo gli ultimi N"""
    if not os.path.exists(exp_dir):
        return 0
    
    logger.info(f"🔧 Pulizia: {exp_dir}")
    
    # Trova tutti i checkpoint
    checkpoints = []
    for item in os.listdir(exp_dir):
        if item.startswith("checkpoint-") and os.path.isdir(os.path.join(exp_dir, item)):
            checkpoints.append(item)
    
    if len(checkpoints) <= keep_last:
        logger.info(f"  ✅ Solo {len(checkpoints)} checkpoint, nessuna pulizia necessaria")
        return 0
    
    # Ordina per numero
    checkpoints.sort(key=get_checkpoint_number)
    
    # Mantieni solo gli ultimi N
    to_delete = checkpoints[:-keep_last]
    space_freed = 0
    
    for checkpoint in to_delete:
        checkpoint_path = os.path.join(exp_dir, checkpoint)
        size = get_size_gb(checkpoint_path)
        
        try:
            shutil.rmtree(checkpoint_path)
            space_freed += size
            logger.info(f"  🗑️ Eliminato: {checkpoint} ({size:.2f}GB)")
        except Exception as e:
            logger.error(f"  ❌ Errore eliminando {checkpoint}: {e}")
    
    # Mostra checkpoint mantenuti
    kept = checkpoints[-keep_last:]
    logger.info(f"  ✅ Mantenuti: {', '.join(kept)}")
    
    return space_freed

def main():
    print("🚨 PULIZIA URGENTE CHECKPOINT")
    print("=" * 50)
    print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    initial_size = get_size_gb(".")
    logger.info(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    
    total_freed = 0
    
    # 1. PULIZIA CHECKPOINT EXPERIMENTS
    logger.info("🔧 PULIZIA CHECKPOINT EXPERIMENTS")
    
    experiments_base = "experiments/xml_direct_input/outputs"
    if os.path.exists(experiments_base):
        for exp_name in os.listdir(experiments_base):
            exp_path = os.path.join(experiments_base, exp_name)
            if os.path.isdir(exp_path):
                # Mantieni più checkpoint per job attivo GEMMA_T9
                keep_count = 3 if "gemma_t9" in exp_name.lower() else 2
                freed = cleanup_experiment_checkpoints(exp_path, keep_count)
                total_freed += freed
    
    # 2. PULIZIA CACHE HUGGINGFACE VECCHIA
    logger.info("🔧 PULIZIA CACHE HUGGINGFACE")
    
    cache_paths = [
        ".cache/huggingface/transformers",
        ".cache/huggingface/datasets", 
    ]
    
    for cache_path in cache_paths:
        if os.path.exists(cache_path):
            cache_size = get_size_gb(cache_path)
            if cache_size > 1.0:  # Solo se >1GB
                try:
                    shutil.rmtree(cache_path)
                    os.makedirs(cache_path, exist_ok=True)
                    total_freed += cache_size
                    logger.info(f"  🗑️ Cache pulita: {cache_path} ({cache_size:.2f}GB)")
                except Exception as e:
                    logger.error(f"  ❌ Errore cache {cache_path}: {e}")
    
    # 3. PULIZIA WANDB RUNS VECCHI
    logger.info("🔧 PULIZIA WANDB RUNS VECCHI")
    
    if os.path.exists("wandb"):
        # Mantieni solo run recenti (ultimi 5)
        runs = []
        for item in os.listdir("wandb"):
            if item.startswith("run-"):
                run_path = os.path.join("wandb", item)
                if os.path.isdir(run_path):
                    mtime = os.path.getmtime(run_path)
                    runs.append((item, mtime, run_path))
        
        # Ordina per data (più recenti prima)
        runs.sort(key=lambda x: x[1], reverse=True)
        
        # Elimina run vecchi (mantieni solo ultimi 5)
        for run_name, _, run_path in runs[5:]:
            run_size = get_size_gb(run_path)
            try:
                shutil.rmtree(run_path)
                total_freed += run_size
                logger.info(f"  🗑️ WandB run eliminato: {run_name} ({run_size:.2f}GB)")
            except Exception as e:
                logger.error(f"  ❌ Errore run {run_name}: {e}")
    
    # 4. PULIZIA LOG VECCHI
    logger.info("🔧 PULIZIA LOG VECCHI")
    
    if os.path.exists("logs"):
        # Mantieni solo log job attivo
        active_job = "2603363"  # GEMMA_T9
        
        for filename in os.listdir("logs"):
            if filename.endswith((".out", ".err")):
                if active_job not in filename:
                    filepath = os.path.join("logs", filename)
                    try:
                        file_size = os.path.getsize(filepath) / (1024**3)
                        os.remove(filepath)
                        total_freed += file_size
                        logger.info(f"  🗑️ Log eliminato: {filename}")
                    except Exception as e:
                        logger.error(f"  ❌ Errore log {filename}: {e}")
    
    # 5. SPAZIO FINALE
    final_size = get_size_gb(".")
    actual_freed = initial_size - final_size
    
    print("\n" + "=" * 50)
    print("🚨 PULIZIA URGENTE COMPLETATA!")
    print("=" * 50)
    logger.info(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    logger.info(f"📊 Spazio finale: {final_size:.2f}GB")
    logger.info(f"🗑️ Spazio liberato: {actual_freed:.2f}GB")
    logger.info(f"🎯 Job attivo: GEMMA_T9 (2603363) - PROTETTO")
    
    print("\n📋 CHECKPOINT MANTENUTI:")
    if os.path.exists("experiments/xml_direct_input/outputs"):
        for exp_name in os.listdir("experiments/xml_direct_input/outputs"):
            exp_path = os.path.join("experiments/xml_direct_input/outputs", exp_name)
            if os.path.isdir(exp_path):
                checkpoints = [f for f in os.listdir(exp_path) if f.startswith("checkpoint-")]
                if checkpoints:
                    checkpoints.sort(key=get_checkpoint_number)
                    print(f"  ✅ {exp_name}: {', '.join(checkpoints[-2:])}")

if __name__ == "__main__":
    main()
