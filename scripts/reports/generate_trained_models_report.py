#!/usr/bin/env python3
"""
Script per generare report completo (HTML + Radar Chart) per i modelli trained.
Basato sui file baseline esistenti, adattato per i 3 modelli trained.
"""

import os
import json
import argparse
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_trained_model_results():
    """Carica i risultati dei 3 modelli trained"""
    models_data = {}
    
    # Percorsi dei risultati dei modelli trained
    model_paths = {
        "Gemma T9": "experiments/xml_direct_input/outputs/gemma_t9_fixed_leonardo/evaluation_results.json",
        "Llama T8": "experiments/xml_direct_input/outputs/llama_t8_24h/evaluation_results.json",
        # Llama T9 non è attualmente in training, ma lasciamo il path per completezza
        "Llama T9": "experiments/xml_direct_input/outputs/llama_t9_fixed_leonardo/evaluation_results.json"
    }
    
    for model_name, path in model_paths.items():
        if os.path.exists(path):
            try:
                with open(path, 'r') as f:
                    data = json.load(f)
                    models_data[model_name] = data
                    logger.info(f"✅ Caricato {model_name}: {path}")
            except Exception as e:
                logger.warning(f"⚠️ Errore caricamento {model_name}: {e}")
                models_data[model_name] = {
                    "bleu_1": 0.0, "bleu_2": 0.0, "bleu_3": 0.0, "bleu_4": 0.0,
                    "meteor": 0.0, "cider": 0.0, "clip_score": 0.0,
                    "status": "Training in progress..."
                }
        else:
            logger.warning(f"⚠️ File non trovato per {model_name}: {path}")
            models_data[model_name] = {
                "bleu_1": 0.0, "bleu_2": 0.0, "bleu_3": 0.0, "bleu_4": 0.0,
                "meteor": 0.0, "cider": 0.0, "clip_score": 0.0,
                "status": "Training in progress..."
            }
    
    return models_data

def create_radar_chart(models_data, output_path, title="Trained Models Performance"):
    """Crea grafico radar per i modelli trained (basato su evaluate_any_model.py)"""
    
    metric_names = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr', 'CLIPScore']
    
    # Prepara i dati
    models_metrics = {}
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
    
    for model_name, data in models_data.items():
        if "status" not in data or data["status"] != "Training in progress...":
            # Modello completato - usa dati reali
            models_metrics[model_name] = [
                data.get("bleu_1", 0) * 100,
                data.get("bleu_2", 0) * 100, 
                data.get("bleu_3", 0) * 100,
                data.get("bleu_4", 0) * 100,
                data.get("meteor", 0) * 100,
                data.get("cider", 0),
                data.get("clip_score", 0) * 100
            ]
        else:
            # Training in corso - usa placeholder
            models_metrics[model_name] = [0, 0, 0, 0, 0, 0, 0]
    
    # Crea il grafico
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # Angoli per ogni metrica
    angles = np.linspace(0, 2 * np.pi, len(metric_names), endpoint=False).tolist()
    angles += angles[:1]  # Chiude il cerchio
    
    # Plotta ogni modello
    for i, (model_name, values) in enumerate(models_metrics.items()):
        values += values[:1]  # Chiude il cerchio
        color = colors[i % len(colors)]
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=color)
        ax.fill(angles, values, alpha=0.25, color=color)
    
    # Personalizza il grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metric_names, fontsize=12)
    ax.set_ylim(0, 100)
    ax.set_yticks([20, 40, 60, 80, 100])
    ax.set_yticklabels(['20', '40', '60', '80', '100'], fontsize=10)
    ax.grid(True)
    
    # Titolo e legenda (STESSO LAYOUT DEL BASELINE)
    plt.title(title, size=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', fontsize=10, frameon=True, fancybox=True, shadow=True)
    
    # Aggiungi dettagli metriche in basso a sinistra (STESSO LAYOUT DEL BASELINE)
    legend_text = []
    for model_name, metrics in models_metrics.items():
        legend_text.append(
            f"{model_name}\n"
            f"BLEU-1: {metrics['BLEU-1']:.3f} | BLEU-2: {metrics['BLEU-2']:.3f} | "
            f"BLEU-3: {metrics['BLEU-3']:.3f} | BLEU-4: {metrics['BLEU-4']:.3f}\n"
            f"METEOR: {metrics['METEOR']:.3f} | CIDEr: {metrics['CIDEr']:.3f} | "
            f"CLIPScore: {metrics['CLIPScore']:.1f}"
        )

    # Posiziona dettagli metriche in basso a sinistra
    plt.figtext(0.02, 0.02, '\n\n'.join(legend_text), fontsize=9, ha='left',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))

    # Salva
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.savefig(output_path.replace('.png', '.pdf'), dpi=300, bbox_inches='tight')
    
    logger.info(f"✅ Grafico radar salvato: {output_path}")
    logger.info(f"✅ Grafico radar PDF: {output_path.replace('.png', '.pdf')}")
    
    plt.close()

def generate_html_report(models_data, output_path, radar_chart_path):
    """Genera report HTML completo per i modelli trained (basato su baseline HTML)"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    html_content = f"""<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Completo Modelli Trained - SVG Captioning</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin: 40px 0;
            padding: 20px;
            border-radius: 10px;
            background: #f8f9fa;
        }}
        .section h2 {{
            color: #495057;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }}
        .radar-section {{
            text-align: center;
            margin: 40px 0;
        }}
        .radar-section img {{
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }}
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }}
        .model-card {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #007bff;
        }}
        .model-card h3 {{
            margin-top: 0;
            color: #007bff;
        }}
        .metric-row {{
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }}
        .metric-row:last-child {{
            border-bottom: none;
        }}
        .metric-name {{
            font-weight: bold;
        }}
        .metric-value {{
            color: #28a745;
            font-weight: bold;
        }}
        .status {{
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.9em;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        .status.completed {{
            background: #d4edda;
            color: #155724;
        }}
        .status.training {{
            background: #fff3cd;
            color: #856404;
        }}
        .footer {{
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #6c757d;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Report Modelli Trained</h1>
            <p>SVG Captioning - Risultati Training Completo</p>
            <p>Generato il: {timestamp}</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 Performance Comparative</h2>
                <div class="radar-section">
                    <img src="{os.path.basename(radar_chart_path)}" alt="Radar Chart Performance">
                    <p><em>Confronto performance tra i 3 modelli trained</em></p>
                </div>
            </div>
            
            <div class="section">
                <h2>📈 Metriche Dettagliate</h2>
                <div class="metrics-grid">"""
    
    # Aggiungi card per ogni modello
    for model_name, data in models_data.items():
        status_class = "completed" if "status" not in data or data["status"] != "Training in progress..." else "training"
        status_text = data.get("status", "Completed")
        
        html_content += f"""
                    <div class="model-card">
                        <h3>{model_name}</h3>
                        <div class="status {status_class}">{status_text}</div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-1:</span>
                            <span class="metric-value">{data.get('bleu_1', 0):.4f}</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-2:</span>
                            <span class="metric-value">{data.get('bleu_2', 0):.4f}</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-3:</span>
                            <span class="metric-value">{data.get('bleu_3', 0):.4f}</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">BLEU-4:</span>
                            <span class="metric-value">{data.get('bleu_4', 0):.4f}</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">METEOR:</span>
                            <span class="metric-value">{data.get('meteor', 0):.4f}</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">CIDEr:</span>
                            <span class="metric-value">{data.get('cider', 0):.4f}</span>
                        </div>
                        <div class="metric-row">
                            <span class="metric-name">CLIPScore:</span>
                            <span class="metric-value">{data.get('clip_score', 0):.4f}</span>
                        </div>
                    </div>"""
    
    html_content += """
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🎯 Report generato automaticamente dal sistema di evaluation</p>
            <p>Versioni: Transformers 4.45.0 + Accelerate 1.2.1 + PEFT 0.4.0</p>
        </div>
    </div>
</body>
</html>"""
    
    # Salva il file HTML
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"✅ Report HTML salvato: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Genera report completo per modelli trained')
    parser.add_argument('--output_dir', default='evaluation_results/', help='Directory output')
    
    args = parser.parse_args()
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Timestamp per i file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Carica dati modelli
    logger.info("📊 Caricamento risultati modelli trained...")
    models_data = load_trained_model_results()
    
    # Genera radar chart
    radar_path = os.path.join(args.output_dir, f"trained_models_radar_{timestamp}.png")
    logger.info("📈 Generazione radar chart...")
    create_radar_chart(models_data, radar_path)
    
    # Genera report HTML
    html_path = os.path.join(args.output_dir, f"trained_models_complete_{timestamp}.html")
    logger.info("📄 Generazione report HTML...")
    generate_html_report(models_data, html_path, radar_path)
    
    print("\n" + "="*80)
    print("🎉 REPORT GENERATO CON SUCCESSO!")
    print("="*80)
    print(f"📈 Radar Chart: {radar_path}")
    print(f"📄 Report HTML: {html_path}")
    print("="*80)

if __name__ == "__main__":
    main()
