#!/bin/bash

echo "🎯 VALUTAZIONE E REPORT MODELLI TRAINED"
echo "======================================"

# Attiva ambiente
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

cd /work/tesi_ediluzio

echo "📊 STEP 1: Valutazione modelli trained..."

# Valuta Gemma T9
if [ -d "experiments/xml_direct_input/outputs/gemma_t9_fixed_leonardo" ]; then
    echo "🔍 Valutazione Gemma T9..."
    python scripts/evaluation/evaluate_trained_models_t7.py \
        --model_path experiments/xml_direct_input/outputs/gemma_t9_fixed_leonardo \
        --output_file experiments/xml_direct_input/outputs/gemma_t9_fixed_leonardo/evaluation_results.json
fi

# Valuta Llama T8
if [ -d "experiments/xml_direct_input/outputs/llama_t8_24h" ]; then
    echo "🔍 Valutazione Llama T8..."
    python scripts/evaluation/evaluate_trained_models_t7.py \
        --model_path experiments/xml_direct_input/outputs/llama_t8_24h \
        --output_file experiments/xml_direct_input/outputs/llama_t8_24h/evaluation_results.json
fi

echo ""
echo "📊 STEP 2: Generazione report HTML + Radar Chart..."

# Genera report completo
bash scripts/reports/generate_report.sh

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ VALUTAZIONE E REPORT COMPLETATI!"
    echo "=================================="
    echo "📁 File generati:"
    ls -la evaluation_results/trained_models_*
    echo ""
    echo "🎯 UTILIZZO:"
    echo "- Apri il file HTML nel browser per vedere il report completo"
    echo "- Il file PNG contiene il radar chart"
    echo "- Confronta con baseline_qualitative_REAL_20250713_151800.html"
else
    echo "❌ Errore nella generazione del report"
fi
