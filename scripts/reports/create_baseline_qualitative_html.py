#!/usr/bin/env python3
"""
Crea report HTML qualitativo con risultati baseline reali
Usa Florence-2 e Idefics3 FIXED di oggi + immagini rasterizzate reali
"""

import json
import os
import base64
from datetime import datetime
import random

def load_baseline_results():
    """Carica i risultati baseline FIXED di oggi"""
    results = {}
    
    # Florence-2 FIXED
    florence_file = "evaluation_results/florence2_FIXED_results_20250713_102506.json"
    if os.path.exists(florence_file):
        with open(florence_file, 'r') as f:
            results['florence2'] = json.load(f)
    
    # Idefics3 FIXED  
    idefics_file = "evaluation_results/idefics3_FIXED_results_20250713_110649.json"
    if os.path.exists(idefics_file):
        with open(idefics_file, 'r') as f:
            results['idefics3'] = json.load(f)
    
    return results

def encode_image_to_base64(image_path):
    """Converte immagine in base64 per embedding HTML"""
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
            base64_string = base64.b64encode(image_data).decode('utf-8')
            return f"data:image/png;base64,{base64_string}"
    except Exception as e:
        print(f"⚠️ Errore encoding immagine {image_path}: {e}")
        return None

def create_qualitative_html():
    """Crea report HTML qualitativo"""
    
    print("📊 CREAZIONE REPORT HTML QUALITATIVO BASELINE")
    print("=============================================")
    
    # Carica risultati
    results = load_baseline_results()
    
    if not results:
        print("❌ Nessun risultato baseline trovato!")
        return
    
    print(f"✅ Caricati risultati per: {list(results.keys())}")
    
    # Seleziona esempi rappresentativi (primi 20 + alcuni casuali)
    total_examples = 400
    selected_indices = list(range(20))  # Primi 20
    selected_indices.extend(random.sample(range(20, total_examples), 30))  # 30 casuali
    selected_indices.sort()
    
    print(f"📋 Selezionati {len(selected_indices)} esempi per il report")
    
    # Inizio HTML
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_content = f"""<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Qualitativo Baseline - SVG Captioning</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        .stats {{
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #dee2e6;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }}
        .stat-card {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }}
        .stat-value {{
            font-size: 1.8em;
            font-weight: bold;
            color: #28a745;
        }}
        .stat-label {{
            color: #6c757d;
            font-size: 0.9em;
        }}
        .example {{
            border-bottom: 1px solid #dee2e6;
            padding: 30px;
            margin-bottom: 20px;
        }}
        .example:last-child {{
            border-bottom: none;
        }}
        .example-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }}
        .example-id {{
            font-size: 1.2em;
            font-weight: bold;
            color: #495057;
        }}
        .image-path {{
            font-size: 0.8em;
            color: #6c757d;
            font-family: monospace;
        }}
        .content-grid {{
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
            align-items: start;
        }}
        .image-section {{
            text-align: center;
        }}
        .example-image {{
            max-width: 100%;
            height: auto;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        .captions-section {{
            display: flex;
            flex-direction: column;
            gap: 20px;
        }}
        .caption-block {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }}
        .caption-block.ground-truth {{
            border-left-color: #28a745;
            background: #d4edda;
        }}
        .caption-block.florence2 {{
            border-left-color: #fd7e14;
            background: #fff3cd;
        }}
        .caption-block.idefics3 {{
            border-left-color: #6f42c1;
            background: #e2e3f3;
        }}
        .caption-label {{
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 0.9em;
            text-transform: uppercase;
        }}
        .caption-text {{
            line-height: 1.5;
        }}
        .footer {{
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            color: #6c757d;
        }}
        @media (max-width: 768px) {{
            .content-grid {{
                grid-template-columns: 1fr;
                gap: 20px;
            }}
            .stats-grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Report Qualitativo Baseline</h1>
            <p>SVG Captioning - Risultati Reali con Immagini Originarie</p>
            <p>Generato il: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="stats">
            <h2>📊 Statistiche Dataset</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">400</div>
                    <div class="stat-label">Esempi Totali</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{len(selected_indices)}</div>
                    <div class="stat-label">Esempi Mostrati</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">2</div>
                    <div class="stat-label">Modelli Baseline</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">PNG</div>
                    <div class="stat-label">Formato Immagini</div>
                </div>
            </div>
        </div>
"""
    
    # Aggiungi esempi
    for i, example_idx in enumerate(selected_indices):
        print(f"📝 Processando esempio {i+1}/{len(selected_indices)} (ID: {example_idx})")
        
        # Ottieni dati per questo esempio
        florence_result = None
        idefics_result = None
        ground_truth = ""
        image_path = ""
        
        if 'florence2' in results and example_idx < len(results['florence2']['results']):
            florence_result = results['florence2']['results'][example_idx]
            ground_truth = florence_result.get('ground_truth', '')
            image_path = florence_result.get('image_path', '')
        
        if 'idefics3' in results and example_idx < len(results['idefics3']['results']):
            idefics_result = results['idefics3']['results'][example_idx]
            if not ground_truth:
                ground_truth = idefics_result.get('ground_truth', '')
            if not image_path:
                image_path = idefics_result.get('image_path', '')
        
        # Encoding immagine
        image_base64 = encode_image_to_base64(image_path) if image_path else None
        
        # Aggiungi esempio all'HTML
        html_content += f"""
        <div class="example">
            <div class="example-header">
                <div class="example-id">Esempio #{example_idx}</div>
                <div class="image-path">{image_path}</div>
            </div>
            
            <div class="content-grid">
                <div class="image-section">
"""
        
        if image_base64:
            html_content += f'<img src="{image_base64}" alt="Esempio {example_idx}" class="example-image">'
        else:
            html_content += '<div style="width: 200px; height: 200px; background: #f8f9fa; border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center; color: #6c757d;">Immagine non disponibile</div>'
        
        html_content += """
                </div>
                
                <div class="captions-section">
"""
        
        # Ground Truth
        html_content += f"""
                    <div class="caption-block ground-truth">
                        <div class="caption-label">✅ Ground Truth</div>
                        <div class="caption-text">{ground_truth}</div>
                    </div>
"""
        
        # Florence-2
        if florence_result:
            florence_caption = florence_result.get('generated_caption', 'N/A')
            # Rimuovi i tag </s><s> che causano strikethrough
            florence_caption = florence_caption.replace('</s><s>', '').replace('</s>', '').replace('<s>', '')
            html_content += f"""
                    <div class="caption-block florence2">
                        <div class="caption-label">🔶 Florence-2</div>
                        <div class="caption-text">{florence_caption}</div>
                    </div>
"""
        
        # Idefics3
        if idefics_result:
            idefics_caption = idefics_result.get('generated_caption', 'N/A')
            html_content += f"""
                    <div class="caption-block idefics3">
                        <div class="caption-label">🔷 Idefics3</div>
                        <div class="caption-text">{idefics_caption}</div>
                    </div>
"""
        
        html_content += """
                </div>
            </div>
        </div>
"""
    
    # Chiudi HTML
    html_content += f"""
        <div class="footer">
            <p>🎯 Report generato automaticamente dal sistema di evaluation</p>
            <p>Dataset: baseline_corrected_400_with_images.json | Immagini: PNG rasterizzate dai chunks originali</p>
            <p>Modelli: Florence-2-FIXED, Idefics3-FIXED | Timestamp: {timestamp}</p>
        </div>
    </div>
</body>
</html>
"""
    
    # Salva file
    output_path = f"evaluation_results/baseline_qualitative_REAL_{timestamp}.html"
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Report HTML salvato: {output_path}")
    print(f"📊 Esempi inclusi: {len(selected_indices)}")
    print(f"🖼️ Immagini embedded: {sum(1 for idx in selected_indices if os.path.exists(results.get('florence2', {}).get('results', [{}])[idx].get('image_path', '')) if 'florence2' in results and idx < len(results['florence2']['results']))}")
    
    return output_path

if __name__ == "__main__":
    create_qualitative_html()
