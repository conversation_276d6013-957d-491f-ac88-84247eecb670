#!/usr/bin/env python3
"""
Genera radar chart identico a quello esistente ma con tutti i BLEU (1-4)
"""

import matplotlib.pyplot as plt
import numpy as np
from math import pi
from datetime import datetime

def create_radar_chart():
    # Dati completi dai risultati baseline
    models_data = {
        'BLIP-2 (400 ex)': {
            'BLEU-1': 0.2381,
            'BLEU-2': 0.1322, 
            'BLEU-3': 0.0757,
            'BLEU-4': 0.0506,
            'METEOR': 0.2227,
            'CIDEr': 0.4428,
            'CLIPScore': 32.54
        },
        'Florence2 (400 ex)': {
            'BLEU-1': 0.2507,
            'BLEU-2': 0.1514,
            'BLEU-3': 0.0675, 
            'BLEU-4': 0.0396,
            'METEOR': 0.4150,
            'CIDEr': 0.4449,
            'CLIPScore': 34.20
        },
        'Idefics3 (100 ex)': {
            'BLEU-1': 0.1269,
            'BLEU-2': 0.1066,
            'BLEU-3': 0.0933,
            'BLEU-4': 0.0789,
            'METEOR': 0.3836,
            'CIDEr': 1.5045,
            'CLIPScore': 33.09
        }
    }
    
    # Metriche per il radar (ordine importante per layout)
    metrics = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr', 'CLIPScore']
    
    # Scale appropriate per ogni metrica (per normalizzazione 0-100%)
    scales = {
        'BLEU-1': 0.3,    # Max ~0.25, scala a 0.3
        'BLEU-2': 0.2,    # Max ~0.15, scala a 0.2  
        'BLEU-3': 0.12,   # Max ~0.09, scala a 0.12
        'BLEU-4': 0.1,    # Max ~0.08, scala a 0.1
        'METEOR': 0.5,    # Max ~0.42, scala a 0.5
        'CIDEr': 2.0,     # Max ~1.5, scala a 2.0
        'CLIPScore': 40   # Max ~34, scala a 40
    }
    
    # Calcola angoli per ogni metrica
    N = len(metrics)
    angles = [n / float(N) * 2 * pi for n in range(N)]
    angles += angles[:1]  # Chiude il cerchio
    
    # Crea figura
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # Colori identici al grafico originale
    colors = ['#e74c3c', '#3498db', '#2ecc71']  # Rosso, Blu, Verde
    
    # Per ogni modello
    for i, (model_name, color) in enumerate(zip(models_data.keys(), colors)):
        # Normalizza i valori (0-100%)
        values = []
        for metric in metrics:
            raw_value = models_data[model_name][metric]
            normalized = (raw_value / scales[metric]) * 100
            values.append(min(normalized, 100))  # Cap a 100%
        
        values += values[:1]  # Chiude il poligono
        
        # Disegna la linea e riempie l'area
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=color)
        ax.fill(angles, values, alpha=0.25, color=color)
    
    # Configurazione assi
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics, fontsize=12, fontweight='bold')
    
    # Griglia radiale
    ax.set_ylim(0, 100)
    ax.set_yticks([20, 40, 60, 80, 100])
    ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'], fontsize=10)
    ax.grid(True, alpha=0.3)
    
    # Titolo
    plt.title('CONFRONTO MODELLI BASELINE\n(Scale Appropriate per Metrica)', 
              size=16, fontweight='bold', pad=30)
    
    # Legenda con dettagli metriche (stile originale)
    legend_labels = []
    for model_name, color in zip(models_data.keys(), colors):
        data = models_data[model_name]
        label = (f"{model_name}\n"
                f"BLEU-1: {data['BLEU-1']:.3f} | BLEU-2: {data['BLEU-2']:.3f} | "
                f"BLEU-3: {data['BLEU-3']:.3f} | BLEU-4: {data['BLEU-4']:.3f}\n"
                f"METEOR: {data['METEOR']:.3f} | CIDEr: {data['CIDEr']:.3f} | "
                f"CLIPScore: {data['CLIPScore']:.1f}")
        legend_labels.append(label)
    
    # Posiziona legenda in alto a destra (LAYOUT AGGIORNATO)
    plt.legend(loc='upper right', fontsize=10, frameon=True, fancybox=True, shadow=True)

    # Posiziona dettagli metriche in basso a sinistra
    plt.figtext(0.02, 0.02, '\n\n'.join(legend_labels), fontsize=9, ha='left',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    # RIMOSSA SCALA PER LAYOUT PULITO
    
    # Layout
    plt.tight_layout()
    
    # Salva
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"evaluation_results/baseline_radar_complete_bleu_{timestamp}.png"
    
    plt.savefig(filename, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"✅ Radar chart salvato: {filename}")
    
    # Statistiche
    print("\n📊 VALORI NORMALIZZATI (0-100%):")
    print("=" * 60)
    for model_name in models_data.keys():
        print(f"\n{model_name}:")
        for metric in metrics:
            raw = models_data[model_name][metric]
            norm = (raw / scales[metric]) * 100
            print(f"  {metric:>10}: {raw:>7.4f} → {norm:>5.1f}%")
    
    return filename

if __name__ == "__main__":
    create_radar_chart()
