#!/usr/bin/env python3
"""
Test del layout radar chart per modelli trained
Simula dati per verificare il layout prima che finiscano i training
"""

import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import os

def test_trained_models_radar():
    """Test layout radar chart con dati simulati"""
    
    # Dati simulati per test layout
    models_data = {
        "Gemma T9": {
            "BLEU-1": 0.245,
            "BLEU-2": 0.148,
            "BLEU-3": 0.089,
            "BLEU-4": 0.058,
            "METEOR": 0.387,
            "CIDEr": 0.892,
            "CLIPScore": 35.2
        },
        "Llama T8": {
            "BLEU-1": 0.251,
            "BLEU-2": 0.152,
            "BLEU-3": 0.092,
            "BLEU-4": 0.061,
            "METEOR": 0.394,
            "CIDEr": 0.915,
            "CLIPScore": 35.8
        }
    }
    
    metric_names = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr', 'CLIPScore']
    
    # Prepara i dati
    models_metrics = {}
    colors = ['#FF6B6B', '#4ECDC4']  # Rosso, Turchese
    
    for model_name in models_data.keys():
        metrics = models_data[model_name]
        
        # Normalizza metriche (0-100%)
        normalized_metrics = {}
        for metric in metric_names:
            if metric.startswith('BLEU'):
                # BLEU normalizzato su scala 0-0.3
                normalized_metrics[metric] = (metrics[metric] / 0.3) * 100
            elif metric == 'METEOR':
                # METEOR normalizzato su scala 0-0.5
                normalized_metrics[metric] = (metrics[metric] / 0.5) * 100
            elif metric == 'CIDEr':
                # CIDEr normalizzato su scala 0-2.0
                normalized_metrics[metric] = (metrics[metric] / 2.0) * 100
            elif metric == 'CLIPScore':
                # CLIPScore normalizzato su scala 0-40
                normalized_metrics[metric] = (metrics[metric] / 40) * 100
        
        models_metrics[model_name] = normalized_metrics
    
    # Crea grafico radar
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # Angoli per ogni metrica
    angles = np.linspace(0, 2 * np.pi, len(metric_names), endpoint=False).tolist()
    angles += angles[:1]  # Chiudi il cerchio
    
    # Plotta ogni modello
    for i, (model_name, color) in enumerate(zip(models_metrics.keys(), colors)):
        values = [models_metrics[model_name][metric] for metric in metric_names]
        values += values[:1]  # Chiudi il poligono
        
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=color)
        ax.fill(angles, values, alpha=0.25, color=color)
    
    # Personalizza il grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metric_names, fontsize=12)
    ax.set_ylim(0, 100)
    ax.set_yticks([20, 40, 60, 80, 100])
    ax.set_yticklabels(['20', '40', '60', '80', '100'], fontsize=10)
    ax.grid(True)
    
    # Titolo e legenda (STESSO LAYOUT DEL BASELINE)
    plt.title("TRAINED MODELS PERFORMANCE\n(Test Layout)", size=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', fontsize=10, frameon=True, fancybox=True, shadow=True)
    
    # Aggiungi dettagli metriche in basso a sinistra (STESSO LAYOUT DEL BASELINE)
    legend_text = []
    for model_name, metrics in models_data.items():
        legend_text.append(
            f"{model_name}\n"
            f"BLEU-1: {metrics['BLEU-1']:.3f} | BLEU-2: {metrics['BLEU-2']:.3f} | "
            f"BLEU-3: {metrics['BLEU-3']:.3f} | BLEU-4: {metrics['BLEU-4']:.3f}\n"
            f"METEOR: {metrics['METEOR']:.3f} | CIDEr: {metrics['CIDEr']:.3f} | "
            f"CLIPScore: {metrics['CLIPScore']:.1f}"
        )
    
    # Posiziona dettagli metriche in basso a sinistra
    plt.figtext(0.02, 0.02, '\n\n'.join(legend_text), fontsize=9, ha='left',
                bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    # Salva
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"evaluation_results/test_trained_models_radar_{timestamp}.png"
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.savefig(output_path.replace('.png', '.pdf'), dpi=300, bbox_inches='tight')
    
    print(f"✅ Test radar chart salvato: {output_path}")
    print(f"✅ Test radar chart PDF: {output_path.replace('.png', '.pdf')}")
    
    plt.close()
    
    return output_path

if __name__ == "__main__":
    print("🧪 TEST LAYOUT RADAR CHART MODELLI TRAINED")
    print("==========================================")
    test_trained_models_radar()
