#!/bin/bash
#SBATCH --job-name=GEMMA_T9
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=16
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --time=24:00:00
#SBATCH --output=logs/GEMMA_T9_2GPU_GRADIENT_ACC_%j.out
#SBATCH --error=logs/GEMMA_T9_2GPU_GRADIENT_ACC_%j.err

echo "🚀 GEMMA T9 - 2 GPU + GRADIENT ACCUMULATION"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

# Setup environment
source ~/.bashrc
conda activate svg_env_new

echo "✅ Python: $(which python)"
echo "✅ CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "✅ GPU count: $(python -c 'import torch; print(torch.cuda.device_count())')"

# Fix PEFT bug automaticamente
echo "🔧 Applicazione fix PEFT bug..."
python -c "
import transformers
import os
trainer_path = os.path.join(os.path.dirname(transformers.__file__), 'trainer.py')
with open(trainer_path, 'r') as f:
    content = f.read()
if 'if len(active_adapters) > 1:' in content:
    content = content.replace(
        'if len(active_adapters) > 1:',
        'if hasattr(active_adapters, \"__len__\") and len(active_adapters) > 1:'
    )
    with open(trainer_path, 'w') as f:
        f.write(content)
    print('✅ Fix PEFT applicato')
else:
    print('✅ Fix PEFT già presente')
"

echo "🚀 Avvio GEMMA T9 2GPU + GRADIENT ACCUMULATION..."

# Esegui training
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation_FIXED.json \
    --output_dir experiments/xml_direct_input/outputs/gemma_t9_gradient_accumulation \
    --disable_quantization

echo "🎉 GEMMA T9 training completato!"
echo "End time: $(date)"

echo "🚀 GEMMA T9 - 2 GPU + GRADIENT ACCUMULATION"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"
echo "🎯 Configurazione: 2 GPU + Gradient Accumulation = 8"
echo "🎯 LoRA: Rank 64, Alpha 128"
echo "🎯 Target Modules: q_proj, k_proj, v_proj, o_proj, gate_proj, up_proj, down_proj"
echo "🎯 Quantization: DISABLED (--disable_quantization)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Configurazione HuggingFace e CUDA
export HF_TOKEN="*************************************"
export HUGGINGFACE_HUB_TOKEN="*************************************"
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:512
export CUDA_LAUNCH_BLOCKING=1
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma_t9_2gpu_gradient_acc
export TOKENIZERS_PARALLELISM=false
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_2gpu_gradient_acc"

echo "✅ Python: $(which python)"
echo "✅ CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "✅ GPU count: $(python -c 'import torch; print(torch.cuda.device_count())')"

echo "✅ Avvio GEMMA T9 2GPU + GRADIENT ACCUMULATION..."
echo "📁 Output: $OUTPUT_DIR"
echo "🔧 Config: experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation_FIXED.json"
echo "📊 Dataset: train_set_corrected_90k.json"
echo "🚀 2 GPU PARALLEL + GRADIENT ACCUMULATION"

# Crea directory output
mkdir -p "$OUTPUT_DIR"

# Training con 2 GPU + gradient accumulation (SENZA quantizzazione)
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29500 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation_FIXED.json \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name gemma_t9_2gpu_gradient_acc

echo "🏁 GEMMA T9 2GPU + GRADIENT ACCUMULATION COMPLETATO"
echo "End time: $(date)"
echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
