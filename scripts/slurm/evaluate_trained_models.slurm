#!/bin/bash
#SBATCH --job-name=EVAL_TRAINED_MODELS
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/EVAL_TRAINED_MODELS_%j.out
#SBATCH --error=logs/EVAL_TRAINED_MODELS_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:1
#SBATCH --mem=48G
#SBATCH --cpus-per-task=8
#SBATCH --time=04:00:00

echo "📊 EVALUATION MODELLI TRAINED - GEMMA T9 + LLAMA T8"
echo "=================================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPU: 1"
echo "Dataset: test_set_10k_RGB.json"
echo "Modelli: Gemma T9 + Llama T8 (trained)"
echo "=================================================="

# Setup environment
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false

# Paths
GEMMA_MODEL="google/gemma-2-9b-it"
LLAMA_MODEL="meta-llama/Llama-3.1-8B-Instruct"
GEMMA_LORA="experiments/xml_direct_input/outputs/gemma_t9_boost_2gpu_quantized"
LLAMA_LORA="experiments/xml_direct_input/outputs/llama_t8_boost_2gpu_gradient_acc"
TEST_FILE="data/processed/FINAL_CORRECT_RGB/test_set_10k_RGB.json"
OUTPUT_DIR="evaluation_results/trained_models_final"

echo "📊 Configurazione:"
echo "   Gemma Model: $GEMMA_MODEL"
echo "   Gemma LoRA: $GEMMA_LORA"
echo "   Llama Model: $LLAMA_MODEL"
echo "   Llama LoRA: $LLAMA_LORA"
echo "   Test File: $TEST_FILE"
echo "   Output: $OUTPUT_DIR"
echo "   Max Examples: 400"

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🔄 Avvio evaluation modelli trained..."

# Evaluation
python scripts/evaluation/evaluate_trained_models_final.py \
    --gemma_model_path "$GEMMA_MODEL" \
    --gemma_lora_path "$GEMMA_LORA" \
    --llama_model_path "$LLAMA_MODEL" \
    --llama_lora_path "$LLAMA_LORA" \
    --test_file "$TEST_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --max_examples 400

echo "✅ Evaluation modelli trained completata!"
echo "📁 Risultati in: $OUTPUT_DIR"
echo "📊 JSON con predictions e ground truth generati"
