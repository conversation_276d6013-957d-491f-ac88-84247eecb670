#!/bin/bash
#SBATCH --job-name=LLAMA_CONTINUE
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_CONTINUE_%j.out
#SBATCH --error=logs/LLAMA_CONTINUE_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --time=12:00:00

echo "🚀 LLAMA T8 CONTINUE - DA CHECKPOINT 15250"
echo "=========================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "Resume: checkpoint-15250"
echo "=========================================="

# Setup environment
cd /work/tesi_ediluzio

# Activate conda environment directly
export PATH="/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin:$PATH"
eval "$(/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/conda shell.bash hook)"
conda activate svg_env_new

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t8_continue

# Paths
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
DATA_FILE="data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json"
CONFIG_PATH="experiments/xml_direct_input/configs/llama_t8_2gpu_final.json"
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_continue"
CHECKPOINT="experiments/xml_direct_input/outputs/llama_t8_restart/checkpoint-15250"

echo "📊 Configurazione:"
echo "   Model: $MODEL_NAME"
echo "   Data: $DATA_FILE"
echo "   Config: $CONFIG_PATH"
echo "   Output: $OUTPUT_DIR"
echo "   Resume: $CHECKPOINT"

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🔄 Resume training da checkpoint 15250..."

# Training
python scripts/training/train_lora_simple.py \
    --model_name "$MODEL_NAME" \
    --data_file "$DATA_FILE" \
    --config_path "$CONFIG_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name llama_t8_continue \
    --resume_from_checkpoint "$CHECKPOINT"

echo "✅ Training completato!"
echo "📁 Output: $OUTPUT_DIR"
