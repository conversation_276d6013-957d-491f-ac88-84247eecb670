#!/bin/bash
#SBATCH --job-name=BASELINE_FAST
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/BASELINE_FAST_%j.out
#SBATCH --error=logs/BASELINE_FAST_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --time=02:00:00

echo "🚀 BASELINE EVALUATION VELOCE - 400 ESEMPI"
echo "=========================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "Ottimizzazioni: Batch processing, conversione veloce"
echo "Timeout: 2 ore (ridotto)"
echo "=========================================="

# Setup environment
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Installa dipendenze ottimizzate
pip install cairosvg pillow --quiet --upgrade

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false

# Paths
DATASET_FILE="data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB.json"
OUTPUT_DIR="evaluation_results/baseline_400_FAST"

echo "📊 Configurazione veloce:"
echo "   Dataset: $DATASET_FILE"
echo "   Output: $OUTPUT_DIR"
echo "   Esempi: 400"
echo "   Batch size: 50 (conversione), 8 (BLIP2)"

# Create output directory
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🔄 Avvio evaluation baseline veloce..."

# Baseline evaluation veloce
python scripts/evaluation/baseline_evaluation_FAST.py \
    --dataset_file "$DATASET_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --max_examples 400

echo "✅ Baseline evaluation veloce completata!"
echo "📁 Risultati: $OUTPUT_DIR"
echo "🖼️ Immagini PNG: $OUTPUT_DIR/images/"
echo "📊 JSON results: $OUTPUT_DIR/*.json"
