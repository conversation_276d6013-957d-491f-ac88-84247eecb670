#!/bin/bash
#SBATCH --job-name=LLAMA_T8_RESUME_7750_NOW
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_T8_RESUME_7750_NOW_%j.out
#SBATCH --error=logs/LLAMA_T8_RESUME_7750_NOW_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 LLAMA T8 TRAINING 24H - LEONARDO"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"
echo "🔄 RESUME: checkpoint-7750 (ULTIMO CHECKPOINT)"

# Cambio directory
cd /work/tesi_ediluzio

# Attivazione ambiente conda
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
echo "✅ Python: $(which python)"
echo "✅ CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

# Configurazione ambiente
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:512

# Configurazione HuggingFace
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface
export TRANSFORMERS_CACHE=/work/tesi_ediluzio/.cache/huggingface/transformers
export HUGGINGFACE_HUB_TOKEN=*************************************

# Configurazione WandB
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t8_resume_7750_leonardo

echo "✅ Avvio LLAMA T8 RESUME 7750 NOW..."
echo "📁 Output: experiments/xml_direct_input/outputs/llama_t8_24h"
echo "🔧 Config: experiments/xml_direct_input/configs/llama_t8_final_optimized.json"
echo "📊 Dataset: train_set_corrected_90k.json"
echo "🔄 RESUME AUTOMATICO: Trova checkpoint-7750"

# Lancio training con resume automatico da checkpoint-7750 (SINGLE GPU + QUANTIZZAZIONE)
python scripts/training/train_lora_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir experiments/xml_direct_input/outputs/llama_t8_24h \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name llama_t8_resume_7750_leonardo_quantized \
    --resume_from_checkpoint experiments/xml_direct_input/outputs/llama_t8_24h/checkpoint-7750

echo "🏁 LLAMA T8 RESUME 7750 NOW COMPLETATO"
echo "End time: $(date)"
