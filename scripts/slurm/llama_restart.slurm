#!/bin/bash
#SBATCH --job-name=LLAMA_RESTART
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_RESTART_%j.out
#SBATCH --error=logs/LLAMA_RESTART_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --time=12:00:00

cd /work/tesi_ediluzio
export PATH="/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin:$PATH"
eval "$(/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/conda shell.bash hook)"
conda activate svg_env_new

export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false

mkdir -p experiments/xml_direct_input/outputs/llama_t8_restart
mkdir -p logs

python scripts/training/train_lora_simple.py \
    --model_name meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_2gpu_final.json \
    --output_dir experiments/xml_direct_input/outputs/llama_t8_restart \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name llama_t8_restart \
    --resume_from_checkpoint experiments/xml_direct_input/outputs/llama_t8_resume_final/checkpoint-13000
