#!/bin/bash
#SBATCH --job-name=COMPARE_ALL_MODELS
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/COMPARE_ALL_MODELS_%j.out
#SBATCH --error=logs/COMPARE_ALL_MODELS_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:0
#SBATCH --mem=16G
#SBATCH --cpus-per-task=4
#SBATCH --time=01:00:00

echo "📊 COMPARAZIONE FINALE - BASELINE vs TRAINED MODELS"
echo "=================================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "Modelli: BLIP-2, Florence2, <PERSON><PERSON><PERSON>s3, <PERSON> T9, <PERSON><PERSON><PERSON> T8"
echo "Output: Radar chart + HTML report completo"
echo "=================================================="

# Setup environment
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Installa dipendenze per metriche
pip install sentence-transformers scikit-learn seaborn --quiet

# Environment variables
export TOKENIZERS_PARALLELISM=false

# Paths
RESULTS_DIR="evaluation_results/all_models_combined"
OUTPUT_DIR="evaluation_results/final_comparison"

echo "📊 Configurazione:"
echo "   Results Dir: $RESULTS_DIR"
echo "   Output Dir: $OUTPUT_DIR"

# Create directories
mkdir -p "$RESULTS_DIR"
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

# Copia tutti i risultati in una directory unificata
echo "🔄 Unificazione risultati..."

# Baseline results
if [ -d "evaluation_results/baseline_400_corretti" ]; then
    cp evaluation_results/baseline_400_corretti/*.json "$RESULTS_DIR/" 2>/dev/null || true
fi

# Trained models results
if [ -d "evaluation_results/trained_models_final" ]; then
    cp evaluation_results/trained_models_final/*.json "$RESULTS_DIR/" 2>/dev/null || true
fi

echo "🔄 Avvio comparazione completa..."

# Comparazione finale
python scripts/evaluation/compare_all_models.py \
    --results_dir "$RESULTS_DIR" \
    --output_dir "$OUTPUT_DIR"

echo "✅ Comparazione finale completata!"
echo "📁 Output: $OUTPUT_DIR"
echo "📊 Radar chart: $OUTPUT_DIR/radar_chart_comparison.png"
echo "📄 HTML report: $OUTPUT_DIR/evaluation_report_complete.html"
echo "📊 Metriche JSON: $OUTPUT_DIR/all_metrics.json"
