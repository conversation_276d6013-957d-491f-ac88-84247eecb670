#!/usr/bin/env python3
"""
Evaluation script per modelli T7 trained con metriche baseline
Confronta performance dei modelli fine-tuned vs baseline BLIP-2
"""

import json
import os
import sys
import argparse
import logging
import time
from datetime import datetime
from pathlib import Path
import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    BitsAndBytesConfig
)
from peft import PeftModel
import numpy as np
from PIL import Image

# Import metriche
sys.path.append('/work/tesi_ediluzio')
from shared.utils.metrics import CaptionEvaluator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class T7ModelEvaluator:
    """Evaluator per modelli T7 trained"""
    
    def __init__(self, model_path, base_model_name, device="auto"):
        self.model_path = model_path
        self.base_model_name = base_model_name
        self.device = device
        self.model = None
        self.tokenizer = None
        
    def load_model(self):
        """Carica modello LoRA trained"""
        logger.info(f"🚀 Caricamento modello T7: {self.model_path}")
        logger.info(f"📦 Base model: {self.base_model_name}")
        
        try:
            # Configurazione quantizzazione (se necessaria)
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
            
            # Carica tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.base_model_name,
                trust_remote_code=True
            )
            
            # Aggiungi pad_token se mancante
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Carica base model
            base_model = AutoModelForCausalLM.from_pretrained(
                self.base_model_name,
                quantization_config=quantization_config,
                device_map=self.device,
                trust_remote_code=True,
                torch_dtype=torch.float16
            )
            
            # Carica LoRA weights
            self.model = PeftModel.from_pretrained(
                base_model,
                self.model_path,
                torch_dtype=torch.float16
            )
            
            # Merge LoRA weights per inference
            self.model = self.model.merge_and_unload()
            
            logger.info("✅ Modello T7 caricato con successo")
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore caricamento modello: {e}")
            return False
    
    def generate_caption(self, svg_content, max_length=150, temperature=0.7):
        """Genera caption da SVG"""
        try:
            # Prepara prompt basato sul modello
            if "gemma" in self.base_model_name.lower():
                prompt = f'<s>[INST] Descrivi questa immagine SVG:\n{svg_content} [/INST]'
            elif "llama" in self.base_model_name.lower():
                prompt = (f'<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\n'
                         f'Descrivi questa immagine SVG:\n{svg_content}<|eot_id|>'
                         f'<|start_header_id|>assistant<|end_header_id|>\n\n')
            else:
                prompt = f"Descrivi questa immagine SVG:\n{svg_content}\nDescrizione:"
            
            # Tokenizza input
            inputs = self.tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=2048,
                padding=True
            ).to(self.model.device)
            
            # Genera caption
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=max_length,
                    temperature=temperature,
                    do_sample=True,
                    top_p=0.9,
                    top_k=50,
                    repetition_penalty=1.1,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # Decodifica output
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Estrai solo la caption generata
            if "gemma" in self.base_model_name.lower():
                if "[/INST]" in generated_text:
                    caption = generated_text.split("[/INST]")[-1].strip()
                else:
                    caption = generated_text.replace(prompt, "").strip()
            elif "llama" in self.base_model_name.lower():
                if "<|start_header_id|>assistant<|end_header_id|>" in generated_text:
                    caption = generated_text.split("<|start_header_id|>assistant<|end_header_id|>")[-1]
                    caption = caption.replace("<|eot_id|>", "").strip()
                else:
                    caption = generated_text.replace(prompt, "").strip()
            else:
                caption = generated_text.replace(prompt, "").strip()
            
            return caption
            
        except Exception as e:
            logger.error(f"❌ Errore generazione caption: {e}")
            return None

def calculate_complete_metrics(predictions, references, image_paths=None):
    """Calcola metriche complete usando CaptionEvaluator (stesse del baseline)"""
    metrics = {}

    # Success rate
    valid_predictions = [p for p in predictions if p and len(p.strip()) > 0]
    metrics['success_rate'] = len(valid_predictions) / len(predictions) if predictions else 0
    metrics['success_count'] = len(valid_predictions)
    metrics['total_examples'] = len(predictions)

    # Lunghezza media predizioni
    if valid_predictions:
        metrics['avg_prediction_length'] = np.mean([len(p.split()) for p in valid_predictions])
        metrics['avg_char_length'] = np.mean([len(p) for p in valid_predictions])
    else:
        metrics['avg_prediction_length'] = 0
        metrics['avg_char_length'] = 0

    # Diversità predizioni
    unique_predictions = len(set(valid_predictions))
    metrics['unique_predictions'] = unique_predictions
    metrics['diversity_rate'] = unique_predictions / len(valid_predictions) if valid_predictions else 0

    # Lunghezza media references
    if references:
        metrics['avg_reference_length'] = np.mean([len(r.split()) for r in references])

    # Calcola metriche NLP complete se ci sono predizioni valide
    if valid_predictions and references:
        logger.info("📊 Calcolo metriche NLP (BLEU-4, CIDEr, ROUGE-L, CLIPScore)...")
        try:
            evaluator = CaptionEvaluator()

            # Prepara dati per evaluator
            eval_references = [[ref] for ref in references]  # Lista di liste per BLEU
            eval_predictions = valid_predictions
            eval_image_paths = image_paths[:len(valid_predictions)] if image_paths else None

            # Calcola tutte le metriche
            nlp_metrics = evaluator.evaluate_all(eval_references, eval_predictions, eval_image_paths)

            # Aggiungi metriche NLP
            metrics.update(nlp_metrics)

            logger.info(f"✅ Metriche NLP calcolate:")
            logger.info(f"  BLEU-4: {nlp_metrics['bleu4']:.4f}")
            logger.info(f"  CIDEr: {nlp_metrics['cider']:.4f}")
            logger.info(f"  ROUGE-L: {nlp_metrics['rougeL']:.4f}")
            logger.info(f"  CLIPScore: {nlp_metrics['clip_score']:.4f}")

        except Exception as e:
            logger.error(f"❌ Errore calcolo metriche NLP: {e}")
            # Aggiungi metriche vuote in caso di errore
            metrics.update({
                'bleu1': 0.0, 'bleu2': 0.0, 'bleu3': 0.0, 'bleu4': 0.0,
                'rouge1': 0.0, 'rouge2': 0.0, 'rougeL': 0.0,
                'cider': 0.0, 'clip_score': 0.0
            })

    return metrics

def evaluate_model(model_evaluator, test_dataset, max_examples=None, output_dir="evaluation_results", image_base_path=None):
    """Evalua un modello sul test dataset"""
    logger.info(f"📊 Inizio evaluation modello: {model_evaluator.model_path}")

    # Limita esempi se specificato
    if max_examples:
        test_data = test_dataset[:max_examples]
        logger.info(f"🎯 Evaluation su {max_examples} esempi")
    else:
        test_data = test_dataset
        logger.info(f"🎯 Evaluation su {len(test_data)} esempi")

    results = []
    predictions = []
    references = []
    image_paths = []

    start_time = time.time()
    
    for i, example in enumerate(test_data):
        try:
            svg_content = example['xml']
            ground_truth = example['caption']
            example_id = example.get('id', f'example_{i}')

            # Costruisci path immagine se disponibile
            image_path = None
            if 'image_path' in example:
                image_path = example['image_path']
            elif image_base_path and example_id:
                # Costruisci path basato su ID (per compatibilità con baseline)
                image_path = f"{image_base_path}/{example_id}.png"

            # Genera caption
            prediction = model_evaluator.generate_caption(svg_content)

            success = prediction is not None and len(prediction.strip()) > 0

            result = {
                'id': example_id,
                'svg_content': svg_content[:200] + "..." if len(svg_content) > 200 else svg_content,
                'ground_truth': ground_truth,
                'prediction': prediction if success else "",
                'success': success,
                'prediction_length': len(prediction.split()) if success else 0,
                'char_length': len(prediction) if success else 0,
                'image_path': image_path
            }

            results.append(result)

            if success:
                predictions.append(prediction)
                references.append(ground_truth)
                if image_path:
                    image_paths.append(image_path)

            # Progress log
            if (i + 1) % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / (i + 1)
                eta = avg_time * (len(test_data) - i - 1)
                logger.info(f"📈 Progress: {i+1}/{len(test_data)} ({(i+1)/len(test_data)*100:.1f}%) - "
                           f"ETA: {eta/60:.1f}min - Avg: {avg_time:.2f}s/example")

        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            results.append({
                'id': example.get('id', f'example_{i}'),
                'svg_content': example['xml'][:200] + "...",
                'ground_truth': example['caption'],
                'prediction': "",
                'success': False,
                'error': str(e)
            })

    # Calcola metriche complete (stesse del baseline)
    metrics = calculate_complete_metrics(predictions, references, image_paths if image_paths else None)
    
    total_time = time.time() - start_time
    metrics['evaluation_time'] = total_time
    metrics['avg_time_per_example'] = total_time / len(test_data)
    
    logger.info(f"✅ Evaluation completata in {total_time/60:.1f} minuti")
    logger.info(f"📊 Success rate: {metrics['success_rate']*100:.1f}% ({metrics['success_count']}/{metrics['total_examples']})")
    logger.info(f"🎯 Diversità: {metrics['unique_predictions']}/{metrics['success_count']} predizioni uniche ({metrics['diversity_rate']*100:.1f}%)")

    # Log metriche NLP se disponibili
    if 'bleu4' in metrics:
        logger.info(f"📈 Metriche NLP:")
        logger.info(f"  BLEU-4: {metrics['bleu4']:.4f}")
        logger.info(f"  CIDEr: {metrics['cider']:.4f}")
        logger.info(f"  ROUGE-L: {metrics['rougeL']:.4f}")
        logger.info(f"  CLIPScore: {metrics['clip_score']:.4f}")
    
    return {
        'results': results,
        'metrics': metrics,
        'predictions': predictions,
        'references': references
    }

def main():
    parser = argparse.ArgumentParser(description='Evaluate T7 trained models')
    parser.add_argument('--gemma_model_path', type=str, help='Path to Gemma T7 LoRA model')
    parser.add_argument('--llama_model_path', type=str, help='Path to Llama T7 LoRA model')
    parser.add_argument('--test_file', type=str,
                       default='data/processed/xml_format_optimized/test_set_100k_final_10000.json',
                       help='Test dataset file')
    parser.add_argument('--max_examples', type=int, default=400, 
                       help='Maximum number of examples to evaluate')
    parser.add_argument('--output_dir', type=str,
                       default='evaluation_results/t7_models_evaluation',
                       help='Output directory for results')
    parser.add_argument('--image_base_path', type=str,
                       default=None,
                       help='Base path for PNG images (for CLIPScore) - Not used for training models')
    
    args = parser.parse_args()
    
    # Crea output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"{args.output_dir}_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    logger.info(f"🚀 EVALUATION MODELLI T7 TRAINED")
    logger.info(f"📁 Output directory: {output_dir}")
    
    # Carica test dataset
    logger.info(f"📊 Caricamento test dataset: {args.test_file}")
    with open(args.test_file, 'r') as f:
        test_dataset = json.load(f)
    
    logger.info(f"✅ Dataset caricato: {len(test_dataset)} esempi")
    
    evaluation_results = {
        'timestamp': timestamp,
        'test_file': args.test_file,
        'max_examples': args.max_examples,
        'models': {}
    }
    
    # Evalua Gemma T7 se specificato
    if args.gemma_model_path and os.path.exists(args.gemma_model_path):
        logger.info(f"\n🦙 EVALUATION GEMMA T7")
        logger.info(f"📦 Model path: {args.gemma_model_path}")
        
        gemma_evaluator = T7ModelEvaluator(
            model_path=args.gemma_model_path,
            base_model_name="google/gemma-2-9b-it"
        )
        
        if gemma_evaluator.load_model():
            gemma_results = evaluate_model(gemma_evaluator, test_dataset, args.max_examples, output_dir, args.image_base_path)
            evaluation_results['models']['gemma_t7'] = gemma_results
            
            # Salva risultati Gemma
            gemma_file = os.path.join(output_dir, 'gemma_t7_results.json')
            with open(gemma_file, 'w') as f:
                json.dump(gemma_results, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Risultati Gemma salvati: {gemma_file}")
        else:
            logger.error("❌ Impossibile caricare modello Gemma T7")
    
    # Evalua Llama T7 se specificato
    if args.llama_model_path and os.path.exists(args.llama_model_path):
        logger.info(f"\n🦙 EVALUATION LLAMA T7")
        logger.info(f"📦 Model path: {args.llama_model_path}")
        
        llama_evaluator = T7ModelEvaluator(
            model_path=args.llama_model_path,
            base_model_name="meta-llama/Llama-3.1-8B-Instruct"
        )
        
        if llama_evaluator.load_model():
            llama_results = evaluate_model(llama_evaluator, test_dataset, args.max_examples, output_dir, args.image_base_path)
            evaluation_results['models']['llama_t7'] = llama_results
            
            # Salva risultati Llama
            llama_file = os.path.join(output_dir, 'llama_t7_results.json')
            with open(llama_file, 'w') as f:
                json.dump(llama_results, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Risultati Llama salvati: {llama_file}")
        else:
            logger.error("❌ Impossibile caricare modello Llama T7")
    
    # Salva risultati completi
    results_file = os.path.join(output_dir, 't7_evaluation_results.json')
    with open(results_file, 'w') as f:
        json.dump(evaluation_results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"\n🎯 EVALUATION T7 COMPLETATA!")
    logger.info(f"📁 Risultati salvati in: {output_dir}")
    logger.info(f"📊 File principale: {results_file}")
    
    # Summary finale
    logger.info(f"\n📈 SUMMARY FINALE:")
    for model_name, results in evaluation_results['models'].items():
        metrics = results['metrics']
        logger.info(f"🔹 {model_name.upper()}:")
        logger.info(f"  Success Rate: {metrics['success_count']}/{metrics['total_examples']} ({metrics['success_rate']*100:.1f}%)")
        logger.info(f"  Diversità: {metrics['unique_predictions']}/{metrics['success_count']} ({metrics['diversity_rate']*100:.1f}%)")
        logger.info(f"  Tempo: {metrics['evaluation_time']/60:.1f} min ({metrics['avg_time_per_example']:.2f}s/esempio)")

        # Metriche NLP se disponibili
        if 'bleu4' in metrics:
            logger.info(f"  📊 Metriche NLP:")
            logger.info(f"    BLEU-4: {metrics['bleu4']:.4f}")
            logger.info(f"    CIDEr: {metrics['cider']:.4f}")
            logger.info(f"    ROUGE-L: {metrics['rougeL']:.4f}")
            logger.info(f"    CLIPScore: {metrics['clip_score']:.4f}")

if __name__ == "__main__":
    main()
