#!/usr/bin/env python3
"""
Comparazione completa: Baseline vs Trained Models
Genera metriche, radar charts e HTML report
"""

import os
import json
import argparse
import logging
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer
import seaborn as sns

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_results(results_dir):
    """Carica tutti i risultati JSON"""
    results = {}
    
    for file in os.listdir(results_dir):
        if file.endswith('_results.json') or file.endswith('.json'):
            model_name = file.replace('_results.json', '').replace('.json', '')
            
            with open(os.path.join(results_dir, file), 'r') as f:
                data = json.load(f)
            
            results[model_name] = data
            logger.info(f"📊 Caricato {model_name}: {len(data)} esempi")
    
    return results

def calculate_metrics(predictions, ground_truths):
    """Calcola metriche di evaluation"""
    
    # Sentence transformer per similarità semantica
    model = SentenceTransformer('all-MiniLM-L6-v2')
    
    # Embeddings
    pred_embeddings = model.encode(predictions)
    gt_embeddings = model.encode(ground_truths)
    
    # Cosine similarity
    similarities = []
    for i in range(len(predictions)):
        sim = cosine_similarity([pred_embeddings[i]], [gt_embeddings[i]])[0][0]
        similarities.append(sim)
    
    # Metriche
    metrics = {
        'semantic_similarity_mean': np.mean(similarities),
        'semantic_similarity_std': np.std(similarities),
        'semantic_similarity_median': np.median(similarities),
        'avg_prediction_length': np.mean([len(p.split()) for p in predictions]),
        'avg_ground_truth_length': np.mean([len(gt.split()) for gt in ground_truths]),
        'num_examples': len(predictions)
    }
    
    return metrics, similarities

def create_radar_chart(all_metrics, output_path):
    """Crea radar chart comparativo"""
    
    # Metriche per radar
    metrics_names = [
        'Semantic Similarity',
        'Length Consistency', 
        'Vocabulary Richness',
        'Coherence Score',
        'Overall Quality'
    ]
    
    # Normalizza metriche per radar
    radar_data = {}
    
    for model_name, metrics in all_metrics.items():
        # Calcola score normalizzati (0-1)
        semantic_sim = metrics['semantic_similarity_mean']
        length_consistency = 1.0 - abs(metrics['avg_prediction_length'] - metrics['avg_ground_truth_length']) / 100
        length_consistency = max(0, min(1, length_consistency))
        
        # Score semplificati per demo
        vocab_richness = min(1.0, metrics['avg_prediction_length'] / 20)
        coherence = semantic_sim  # Usa semantic similarity come proxy
        overall = (semantic_sim + length_consistency + vocab_richness + coherence) / 4
        
        radar_data[model_name] = [
            semantic_sim,
            length_consistency,
            vocab_richness,
            coherence,
            overall
        ]
    
    # Plot radar chart
    angles = np.linspace(0, 2 * np.pi, len(metrics_names), endpoint=False).tolist()
    angles += angles[:1]  # Chiudi il cerchio
    
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    
    for i, (model_name, values) in enumerate(radar_data.items()):
        values += values[:1]  # Chiudi il cerchio
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=colors[i % len(colors)])
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics_names)
    ax.set_ylim(0, 1)
    ax.set_title('Model Performance Comparison', size=16, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax.grid(True)
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"📊 Radar chart salvato: {output_path}")

def generate_html_report(all_results, all_metrics, output_path):
    """Genera HTML report completo"""
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>SVG Captioning - Model Evaluation Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .header {{ text-align: center; margin-bottom: 40px; }}
            .metrics-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
            .metrics-table th, .metrics-table td {{ border: 1px solid #ddd; padding: 12px; text-align: center; }}
            .metrics-table th {{ background-color: #f2f2f2; }}
            .example {{ margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }}
            .model-name {{ font-weight: bold; color: #333; }}
            .prediction {{ background-color: #f9f9f9; padding: 10px; margin: 5px 0; border-radius: 3px; }}
            .ground-truth {{ background-color: #e8f5e8; padding: 10px; margin: 5px 0; border-radius: 3px; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎯 SVG Captioning Model Evaluation Report</h1>
            <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <h2>📊 Performance Metrics</h2>
        <table class="metrics-table">
            <tr>
                <th>Model</th>
                <th>Semantic Similarity</th>
                <th>Avg Prediction Length</th>
                <th>Avg Ground Truth Length</th>
                <th>Examples</th>
            </tr>
    """
    
    # Aggiungi metriche
    for model_name, metrics in all_metrics.items():
        html_content += f"""
            <tr>
                <td class="model-name">{model_name}</td>
                <td>{metrics['semantic_similarity_mean']:.3f} ± {metrics['semantic_similarity_std']:.3f}</td>
                <td>{metrics['avg_prediction_length']:.1f}</td>
                <td>{metrics['avg_ground_truth_length']:.1f}</td>
                <td>{metrics['num_examples']}</td>
            </tr>
        """
    
    html_content += """
        </table>
        
        <h2>📈 Performance Radar Chart</h2>
        <img src="radar_chart_comparison.png" alt="Radar Chart" style="max-width: 100%; height: auto;">
        
        <h2>📝 Example Predictions</h2>
    """
    
    # Aggiungi esempi
    for i in range(min(5, len(list(all_results.values())[0]))):
        html_content += f'<div class="example"><h3>Example {i+1}</h3>'
        
        # Ground truth
        gt = list(all_results.values())[0][i]['ground_truth']
        html_content += f'<div class="ground-truth"><strong>Ground Truth:</strong> {gt}</div>'
        
        # Predictions per ogni modello
        for model_name, results in all_results.items():
            if i < len(results):
                pred = results[i]['prediction']
                html_content += f'<div class="prediction"><strong>{model_name}:</strong> {pred}</div>'
        
        html_content += '</div>'
    
    html_content += """
        </body>
    </html>
    """
    
    with open(output_path, 'w') as f:
        f.write(html_content)
    
    logger.info(f"📄 HTML report salvato: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Comparazione completa modelli")
    parser.add_argument("--results_dir", required=True, help="Directory con risultati JSON")
    parser.add_argument("--output_dir", required=True, help="Directory output")
    
    args = parser.parse_args()
    
    logger.info("🚀 COMPARAZIONE COMPLETA MODELLI")
    logger.info("=" * 50)
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Carica risultati
    all_results = load_results(args.results_dir)
    
    if not all_results:
        logger.error("❌ Nessun risultato trovato!")
        return
    
    # Calcola metriche per ogni modello
    all_metrics = {}
    
    for model_name, results in all_results.items():
        predictions = [r['prediction'] for r in results]
        ground_truths = [r['ground_truth'] for r in results]
        
        metrics, similarities = calculate_metrics(predictions, ground_truths)
        all_metrics[model_name] = metrics
        
        logger.info(f"📊 {model_name}: Semantic Similarity = {metrics['semantic_similarity_mean']:.3f}")
    
    # Genera radar chart
    radar_path = os.path.join(args.output_dir, "radar_chart_comparison.png")
    create_radar_chart(all_metrics, radar_path)
    
    # Genera HTML report
    html_path = os.path.join(args.output_dir, "evaluation_report_complete.html")
    generate_html_report(all_results, all_metrics, html_path)
    
    # Salva metriche JSON
    metrics_path = os.path.join(args.output_dir, "all_metrics.json")
    with open(metrics_path, 'w') as f:
        json.dump(all_metrics, f, indent=2)
    
    logger.info("=" * 50)
    logger.info("🎉 COMPARAZIONE COMPLETATA!")
    logger.info(f"📁 Output: {args.output_dir}")
    logger.info(f"📊 Radar Chart: {radar_path}")
    logger.info(f"📄 HTML Report: {html_path}")
    logger.info(f"📊 Metriche JSON: {metrics_path}")

if __name__ == "__main__":
    main()
