#!/usr/bin/env python3
"""
Script per fare merge offline di Llama T8 LoRA weights
Soluzione da HuggingFace Forum: https://discuss.huggingface.co/t/help-with-merging-lora-weights-back-into-base-model/40968
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel
import os
import argparse

def merge_lora_offline(base_model_path, lora_adapter_path, output_path):
    """
    Merge LoRA weights offline e salva il modello merged
    """
    print(f"🚀 MERGE OFFLINE LLAMA T8 LORA")
    print(f"📁 Base model: {base_model_path}")
    print(f"📁 LoRA adapter: {lora_adapter_path}")
    print(f"📁 Output: {output_path}")
    
    # 1. Carica base model in half precision (come da forum)
    print("🔧 Caricamento base model...")
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_path,
        torch_dtype=torch.float16,
        device_map="cpu",  # Carica su CPU per evitare problemi memoria
        trust_remote_code=True
    )
    
    # 2. Carica tokenizer
    print("🔧 Caricamento tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(base_model_path)
    
    # 3. Carica LoRA adapter e merge (soluzione da forum)
    print("🔧 Caricamento LoRA adapter...")
    model_to_merge = PeftModel.from_pretrained(base_model, lora_adapter_path)
    
    # 4. Merge LoRA weights nel base model
    print("🔧 Merge LoRA weights...")
    merged_model = model_to_merge.merge_and_unload()
    
    # 5. Salva modello merged
    print(f"💾 Salvataggio modello merged in: {output_path}")
    os.makedirs(output_path, exist_ok=True)
    merged_model.save_pretrained(output_path)
    tokenizer.save_pretrained(output_path)
    
    print("✅ MERGE COMPLETATO!")
    print(f"📁 Modello merged salvato in: {output_path}")
    
    return output_path

def main():
    parser = argparse.ArgumentParser(description="Merge LoRA weights offline")
    parser.add_argument("--base_model", required=True, help="Path al base model")
    parser.add_argument("--lora_adapter", required=True, help="Path al LoRA adapter")
    parser.add_argument("--output_path", required=True, help="Path output per modello merged")
    
    args = parser.parse_args()
    
    merge_lora_offline(args.base_model, args.lora_adapter, args.output_path)

if __name__ == "__main__":
    main()
