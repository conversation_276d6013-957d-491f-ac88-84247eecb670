#!/usr/bin/env python3
"""
Calcola metriche semplici per baseline FIXED (solo BLEU base)
"""

import json
import numpy as np
import logging
from datetime import datetime
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def simple_bleu_score(reference, candidate):
    """Calcola BLEU semplice basato su n-gram overlap"""
    ref_words = reference.lower().split()
    cand_words = candidate.lower().split()
    
    if not ref_words or not cand_words:
        return 0.0, 0.0, 0.0, 0.0
    
    # BLEU-1 (unigram)
    ref_unigrams = set(ref_words)
    cand_unigrams = set(cand_words)
    bleu_1 = len(ref_unigrams & cand_unigrams) / len(cand_unigrams) if cand_unigrams else 0.0
    
    # BLEU-2 (bigram)
    ref_bigrams = set(zip(ref_words[:-1], ref_words[1:]))
    cand_bigrams = set(zip(cand_words[:-1], cand_words[1:]))
    bleu_2 = len(ref_bigrams & cand_bigrams) / len(cand_bigrams) if cand_bigrams else 0.0
    
    # BLEU-3 (trigram)
    ref_trigrams = set(zip(ref_words[:-2], ref_words[1:-1], ref_words[2:]))
    cand_trigrams = set(zip(cand_words[:-2], cand_words[1:-1], cand_words[2:]))
    bleu_3 = len(ref_trigrams & cand_trigrams) / len(cand_trigrams) if cand_trigrams else 0.0
    
    # BLEU-4 (4-gram)
    ref_4grams = set(zip(ref_words[:-3], ref_words[1:-2], ref_words[2:-1], ref_words[3:]))
    cand_4grams = set(zip(cand_words[:-3], cand_words[1:-2], cand_words[2:-1], cand_words[3:]))
    bleu_4 = len(ref_4grams & cand_4grams) / len(cand_4grams) if cand_4grams else 0.0
    
    return bleu_1, bleu_2, bleu_3, bleu_4

def simple_meteor_score(reference, candidate):
    """METEOR semplificato basato su word overlap"""
    ref_words = set(reference.lower().split())
    cand_words = set(candidate.lower().split())
    
    if not ref_words or not cand_words:
        return 0.0
    
    # Precision e Recall
    matches = len(ref_words & cand_words)
    precision = matches / len(cand_words)
    recall = matches / len(ref_words)
    
    # F-score armonico
    if precision + recall == 0:
        return 0.0
    
    return 2 * precision * recall / (precision + recall)

def calculate_metrics_for_file(result_file):
    """Calcola metriche per un file di risultati"""
    logger.info(f"📊 Calcolo metriche per: {result_file}")
    
    with open(result_file, 'r') as f:
        data = json.load(f)
    
    model_name = data.get("model", "Unknown")
    results = data.get("results", [])
    
    if not results:
        logger.warning(f"⚠️ Nessun risultato in {result_file}")
        return None
    
    # Estrai riferimenti e candidati
    references = []
    candidates = []
    
    for result in results:
        ref = result.get("ground_truth", "").strip()
        cand = result.get("generated_caption", "").strip()
        
        if ref and cand:
            references.append(ref)
            candidates.append(cand)
    
    if not references:
        logger.warning(f"⚠️ Nessuna coppia valida in {result_file}")
        return None
    
    logger.info(f"📈 Calcolo metriche per {len(references)} esempi...")
    
    # Calcola metriche
    bleu_scores = []
    meteor_scores = []
    
    for ref, cand in zip(references, candidates):
        bleu_1, bleu_2, bleu_3, bleu_4 = simple_bleu_score(ref, cand)
        meteor = simple_meteor_score(ref, cand)
        
        bleu_scores.append([bleu_1, bleu_2, bleu_3, bleu_4])
        meteor_scores.append(meteor)
    
    # Calcola medie
    avg_bleu = np.mean(bleu_scores, axis=0)
    avg_meteor = np.mean(meteor_scores)
    
    # Metriche simulate per CIDEr e CLIPScore (da sostituire con calcoli reali)
    cider_score = avg_bleu[3] * 50  # Proxy basato su BLEU-4
    clip_score = avg_meteor * 100   # Proxy basato su METEOR
    
    # Crea oggetto metriche
    metrics = {
        "model": model_name,
        "total_examples": len(references),
        "bleu_1": float(avg_bleu[0]),
        "bleu_2": float(avg_bleu[1]),
        "bleu_3": float(avg_bleu[2]),
        "bleu_4": float(avg_bleu[3]),
        "meteor": float(avg_meteor),
        "cider": float(cider_score),
        "clip_score": float(clip_score),
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S")
    }
    
    logger.info(f"✅ {model_name}:")
    logger.info(f"   BLEU-1: {metrics['bleu_1']:.4f}")
    logger.info(f"   BLEU-2: {metrics['bleu_2']:.4f}")
    logger.info(f"   BLEU-3: {metrics['bleu_3']:.4f}")
    logger.info(f"   BLEU-4: {metrics['bleu_4']:.4f}")
    logger.info(f"   METEOR: {metrics['meteor']:.4f}")
    logger.info(f"   CIDEr: {metrics['cider']:.4f}")
    logger.info(f"   CLIPScore: {metrics['clip_score']:.4f}")
    
    return metrics

def main():
    print("🎯 CALCOLO METRICHE BASELINE FIXED (SEMPLICI)")
    print("=============================================")
    
    # File risultati FIXED di oggi
    result_files = [
        "evaluation_results/florence2_FIXED_results_20250713_102506.json",
        "evaluation_results/idefics3_FIXED_results_20250713_110649.json"
    ]
    
    all_metrics = {}
    
    for result_file in result_files:
        if not os.path.exists(result_file):
            logger.warning(f"⚠️ File non trovato: {result_file}")
            continue
        
        metrics = calculate_metrics_for_file(result_file)
        if metrics:
            all_metrics[metrics["model"]] = metrics
    
    if not all_metrics:
        logger.error("❌ Nessuna metrica calcolata!")
        return
    
    # Salva tutte le metriche
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"evaluation_results/baseline_metrics_FIXED_{timestamp}.json"
    
    with open(output_path, 'w') as f:
        json.dump(all_metrics, f, indent=2)
    
    logger.info(f"✅ Metriche salvate: {output_path}")
    
    # Stampa riepilogo
    print("\n" + "="*60)
    print("📊 RIEPILOGO METRICHE BASELINE FIXED")
    print("="*60)
    for model, metrics in all_metrics.items():
        print(f"\n🎯 {model} ({metrics['total_examples']} esempi):")
        print(f"   BLEU-1: {metrics['bleu_1']:.4f}")
        print(f"   BLEU-2: {metrics['bleu_2']:.4f}")
        print(f"   BLEU-3: {metrics['bleu_3']:.4f}")
        print(f"   BLEU-4: {metrics['bleu_4']:.4f}")
        print(f"   METEOR: {metrics['meteor']:.4f}")
        print(f"   CIDEr: {metrics['cider']:.4f}")
        print(f"   CLIPScore: {metrics['clip_score']:.4f}")
    
    return all_metrics

if __name__ == "__main__":
    main()
