# 📊 Sistema di Valutazione Modelli - SVG Captioning

Sistema completo per valutare qualsiasi modello di caption generation con tutte le metriche standard e generazione automatica di grafici radar.

## 🚀 Quick Start

```bash
# Valuta un singolo modello
python scripts/evaluation/evaluate_any_model.py \
    --results_file path/to/your_model_results.json \
    --model_name "YourModelName" \
    --output_dir evaluation_results/your_model/

# Valuta e confronta con baseline
python scripts/evaluation/evaluate_any_model.py \
    --results_file path/to/your_model_results.json \
    --model_name "YourModelName" \
    --output_dir evaluation_results/comparison/ \
    --compare_with evaluation_results/baseline_optimized/baseline_metrics_complete.json
```

## 📋 Formato File Risultati

Il tuo file JSON deve avere questa struttura:

```json
[
  {
    "example_id": 0,
    "prediction": "The image shows a simple black and white illustration of a car",
    "ground_truth": "A black and white drawing of an automobile",
    "success": true
  },
  {
    "example_id": 1,
    "prediction": "Another generated caption...",
    "ground_truth": "Reference caption...",
    "success": true
  }
]
```

### Campi Richiesti:
- `example_id`: ID dell'esempio (usato per mappare alle immagini)
- `prediction`: Caption generata dal tuo modello
- `ground_truth`: Caption di riferimento
- `success`: true se la generazione è riuscita, false altrimenti

### Campi Opzionali:
- `image_path`: Percorso diretto all'immagine (se disponibile)

## 📊 Metriche Calcolate

### Metriche Testuali:
- **BLEU-1, BLEU-2, BLEU-3, BLEU-4**: Sovrapposizione n-grammi
- **METEOR**: Allineamento semantico con sinonimi
- **CIDEr**: Consensus-based Image Description Evaluation
- **ROUGE-1, ROUGE-2, ROUGE-L**: Recall-oriented metrics

### Metriche Multimodali:
- **CLIPScore**: Similarità immagine-testo usando CLIP (0-100)

### Metriche di Diversità:
- **Diversity Rate**: Percentuale di predizioni uniche
- **Success Rate**: Percentuale di esempi processati con successo

## 🎯 Output Generato

### 1. File Metriche JSON
```
your_model_metrics_2025-07-03_15-30-45.json
```
Contiene tutte le metriche calcolate in formato strutturato.

### 2. Grafico Radar
```
your_model_radar_chart_2025-07-03_15-30-45.png
your_model_radar_chart_2025-07-03_15-30-45.pdf
```
Visualizzazione radar con:
- Tutte le 7 metriche principali
- Valori nella legenda
- Tabella dettagliata
- Confronto con altri modelli (se specificato)

### 3. Log Dettagliato
Output completo con:
- Statistiche di caricamento
- Progress delle metriche
- Risultati finali
- Eventuali warning/errori

## 🔧 Esempi di Utilizzo

### Valutazione Singola
```bash
python scripts/evaluation/evaluate_any_model.py \
    --results_file evaluation_results/gemma/gemma_results.json \
    --model_name "Gemma-2B-SVG" \
    --output_dir evaluation_results/gemma/
```

### Confronto con Baseline
```bash
python scripts/evaluation/evaluate_any_model.py \
    --results_file evaluation_results/my_model/results.json \
    --model_name "MyModel" \
    --output_dir evaluation_results/comparison/ \
    --compare_with evaluation_results/baseline_optimized/baseline_metrics_complete.json
```

### Confronto Multi-Modello
```bash
# Valuta primo modello
python scripts/evaluation/evaluate_any_model.py \
    --results_file model1_results.json \
    --model_name "Model1" \
    --output_dir comparison/

# Aggiungi secondo modello
python scripts/evaluation/evaluate_any_model.py \
    --results_file model2_results.json \
    --model_name "Model2" \
    --output_dir comparison/ \
    --compare_with comparison/Model1_metrics_*.json
```

## 📁 Struttura File

```
scripts/evaluation/
├── evaluate_any_model.py      # Script principale
├── example_usage.sh           # Esempi di utilizzo
├── README_evaluation.md       # Questa documentazione
├── calculate_baseline_metrics_100.py  # Script baseline (riferimento)
└── test_clip_score.py         # Test CLIPScore

evaluation_results/
├── baseline_optimized/
│   └── baseline_metrics_complete.json  # Metriche baseline di riferimento
├── your_model/
│   ├── your_model_metrics_*.json
│   └── your_model_radar_chart_*.png
└── comparison/
    ├── comparison_metrics_*.json
    └── comparison_radar_chart_*.png
```

## ⚙️ Configurazione

### Dipendenze
Lo script usa automaticamente:
- CLIP model: `openai/clip-vit-base-patch32`
- Dataset mapping: `/work/tesi_ediluzio/data/processed/xml_format_optimized/baseline_t7_corrected_400_with_images.json`

### Personalizzazione
Puoi modificare:
- Modello CLIP in `shared/utils/metrics.py`
- Colori grafico in `evaluate_any_model.py`
- Metriche visualizzate nel radar chart

## 🎨 Personalizzazione Grafico

Il grafico radar include automaticamente:
- ✅ Normalizzazione corretta (CLIPScore 0-100 → 0-1)
- ✅ Colori distinti per ogni modello
- ✅ Legenda con valori principali
- ✅ Tabella dettagliata laterale
- ✅ Note esplicative
- ✅ Export PNG e PDF

## 🚨 Troubleshooting

### Errore "Image not found"
- Verifica che `example_id` corrisponda agli indici del dataset
- Controlla che le immagini esistano in `baseline_t7_images_full/`

### CLIPScore = 0
- Verifica che le immagini siano accessibili
- Controlla che i testi non siano vuoti
- Verifica memoria disponibile per CLIP

### Errori di memoria
- Riduci batch size in `shared/utils/metrics.py`
- Usa CPU invece di GPU se necessario

## 📈 Interpretazione Risultati

### Valori di Riferimento (Baseline):
- **BLEU-4**: 0.01-0.03 (baseline)
- **METEOR**: 0.18-0.32 (baseline)
- **CIDEr**: 0.20-0.25 (baseline)
- **CLIPScore**: 30-32 (baseline)

### Cosa Cercare:
- **BLEU-4 > 0.03**: Buona sovrapposizione lessicale
- **METEOR > 0.30**: Buon allineamento semantico
- **CLIPScore > 35**: Buona correlazione immagine-testo
- **Diversity > 0.95**: Buona varietà nelle predizioni

## 🎉 Pronto per l'Uso!

Lo script è completamente automatizzato e pronto per valutare i tuoi modelli. Basta preparare il file JSON con i risultati e lanciare il comando! 🚀
