#!/usr/bin/env python3
"""
Evaluation completa dei modelli trained (Gemma T9 + Llama T8)
"""

import os
import json
import argparse
import logging
from datetime import datetime
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
from tqdm import tqdm

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_trained_model(base_model_path, lora_path, device="cuda"):
    """Carica modello trained con LoRA"""
    logger.info(f"🔄 Caricamento modello: {base_model_path}")
    logger.info(f"🔄 Caricamento LoRA: {lora_path}")
    
    # Tokenizer
    tokenizer = AutoTokenizer.from_pretrained(base_model_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Modello base
    model = AutoModelForCausalLM.from_pretrained(
        base_model_path,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True
    )
    
    # Applica LoRA
    model = PeftModel.from_pretrained(model, lora_path)
    model = model.merge_and_unload()
    
    return model, tokenizer

def generate_caption(model, tokenizer, xml_content, max_length=512):
    """Genera caption per un SVG"""
    
    # Prompt basato sul modello
    if "gemma" in tokenizer.name_or_path.lower():
        prompt = f"<bos><start_of_turn>user\nDescribe this SVG:\n{xml_content}<end_of_turn>\n<start_of_turn>model\n"
    elif "llama" in tokenizer.name_or_path.lower():
        prompt = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\nDescribe this SVG:\n{xml_content}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
    else:
        prompt = f"Describe this SVG:\n{xml_content}\n\nCaption: "
    
    # Tokenizza
    inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=1536)
    inputs = {k: v.to(model.device) for k, v in inputs.items()}
    
    # Genera
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            pad_token_id=tokenizer.eos_token_id
        )
    
    # Decodifica
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Estrai solo la caption generata
    if "gemma" in tokenizer.name_or_path.lower():
        if "<start_of_turn>model\n" in generated_text:
            caption = generated_text.split("<start_of_turn>model\n")[-1].strip()
        else:
            caption = generated_text.replace(prompt, "").strip()
    elif "llama" in tokenizer.name_or_path.lower():
        if "<|start_header_id|>assistant<|end_header_id|>" in generated_text:
            caption = generated_text.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
            caption = caption.replace("<|eot_id|>", "").strip()
        else:
            caption = generated_text.replace(prompt, "").strip()
    else:
        caption = generated_text.replace(prompt, "").strip()
    
    return caption

def evaluate_model(model_path, lora_path, test_data, model_name, output_dir, max_examples=400):
    """Evalua un modello trained"""
    logger.info(f"🚀 Evaluation {model_name}")
    
    # Carica modello
    model, tokenizer = load_trained_model(model_path, lora_path)
    
    results = []
    
    # Limita esempi
    test_data = test_data[:max_examples]
    
    for i, example in enumerate(tqdm(test_data, desc=f"Evaluating {model_name}")):
        try:
            xml_content = example['xml']
            ground_truth = example['caption']
            
            # Genera caption
            prediction = generate_caption(model, tokenizer, xml_content)
            
            result = {
                'id': i,
                'xml': xml_content,
                'ground_truth': ground_truth,
                'prediction': prediction,
                'model': model_name,
                'filename': example.get('filename', f'example_{i}')
            }
            
            results.append(result)
            
            if (i + 1) % 50 == 0:
                logger.info(f"   📈 Processati {i+1}/{len(test_data)} esempi")
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            continue
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(output_dir, f"{model_name}_results_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ {model_name} completato: {results_file}")
    return results_file

def main():
    parser = argparse.ArgumentParser(description="Evaluation modelli trained")
    parser.add_argument("--gemma_model_path", required=True, help="Path modello Gemma base")
    parser.add_argument("--gemma_lora_path", required=True, help="Path LoRA Gemma")
    parser.add_argument("--llama_model_path", required=True, help="Path modello Llama base")
    parser.add_argument("--llama_lora_path", required=True, help="Path LoRA Llama")
    parser.add_argument("--test_file", required=True, help="File test dataset")
    parser.add_argument("--output_dir", required=True, help="Directory output")
    parser.add_argument("--max_examples", type=int, default=400, help="Numero massimo esempi")
    
    args = parser.parse_args()
    
    logger.info("🚀 EVALUATION MODELLI TRAINED")
    logger.info("=" * 50)
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Carica test data
    with open(args.test_file, 'r') as f:
        test_data = json.load(f)
    
    logger.info(f"📊 Test dataset: {len(test_data)} esempi")
    logger.info(f"📊 Max esempi: {args.max_examples}")
    
    results_files = []
    
    # Evalua Gemma T9
    if os.path.exists(args.gemma_lora_path):
        gemma_results = evaluate_model(
            args.gemma_model_path,
            args.gemma_lora_path,
            test_data,
            "Gemma_T9",
            args.output_dir,
            args.max_examples
        )
        results_files.append(gemma_results)
    else:
        logger.warning(f"⚠️ Gemma LoRA non trovato: {args.gemma_lora_path}")
    
    # Evalua Llama T8
    if os.path.exists(args.llama_lora_path):
        llama_results = evaluate_model(
            args.llama_model_path,
            args.llama_lora_path,
            test_data,
            "Llama_T8",
            args.output_dir,
            args.max_examples
        )
        results_files.append(llama_results)
    else:
        logger.warning(f"⚠️ Llama LoRA non trovato: {args.llama_lora_path}")
    
    logger.info("=" * 50)
    logger.info("🎉 EVALUATION COMPLETATA!")
    logger.info(f"📁 Risultati in: {args.output_dir}")
    for rf in results_files:
        logger.info(f"📊 File: {rf}")

if __name__ == "__main__":
    main()
