#!/usr/bin/env python3
"""
🚀 BASELINE EVALUATION VELOCE - CONVERSIONE SVG OTTIMIZZATA
"""

import os
import json
import argparse
import logging
from datetime import datetime
import subprocess
from PIL import Image, ImageDraw
import io
import base64

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def de_parser_fast(svg_data):
    """Versione veloce de_parser con sfondo bianco"""
    res = '<?xml version="1.0" encoding="utf-8"?>\n'
    res += '<svg viewBox="0 0 512 512" width="512" height="512" xmlns="http://www.w3.org/2000/svg">\n'
    res += '<rect width="512" height="512" fill="white"/>\n'
    
    # Parsing veloce
    svg_data = svg_data.replace("style=", "<path style=\"")
    svg_data = svg_data.replace("\t", "\" d=\"")
    svg_data = svg_data.replace("\n", "Z\" />\n")
    
    res += svg_data
    res += "</svg>"
    
    return res

def svg_to_png_fast(svg_content, output_path, size=512):
    """Conversione SVG→PNG veloce - SOLO INKSCAPE (più stabile)"""

    # Metodo 1: Inkscape command line (più stabile)
    try:
        # Salva SVG temporaneo
        temp_svg = output_path.replace('.png', '_temp.svg')
        with open(temp_svg, 'w') as f:
            f.write(svg_content)

        # Converti con Inkscape
        result = subprocess.run([
            'inkscape', temp_svg,
            '--export-type=png',
            f'--export-filename={output_path}',
            f'--export-width={size}',
            f'--export-height={size}',
            '--export-background=white',
            '--export-background-opacity=1.0'
        ], capture_output=True, timeout=10, text=True)

        # Rimuovi SVG temporaneo
        if os.path.exists(temp_svg):
            os.remove(temp_svg)

        if result.returncode == 0 and os.path.exists(output_path):
            return True
        else:
            logger.debug(f"Inkscape error: {result.stderr}")

    except subprocess.TimeoutExpired:
        logger.debug(f"Inkscape timeout per {output_path}")
    except Exception as e:
        logger.debug(f"Inkscape fallito: {e}")

    # Metodo 2: Immagine placeholder (fallback veloce)
    try:
        img = Image.new('RGB', (size, size), 'white')
        draw = ImageDraw.Draw(img)

        # Testo più informativo
        text_lines = [
            "SVG Conversion",
            "Not Available",
            f"Image {os.path.basename(output_path)}"
        ]

        y_start = size // 4
        for i, line in enumerate(text_lines):
            draw.text((size//8, y_start + i*30), line, fill='black')

        img.save(output_path)
        return True
    except Exception as e:
        logger.error(f"Anche placeholder fallito: {e}")
        return False

def create_images_batch(dataset_file, output_dir, batch_size=50):
    """Crea immagini in batch per efficienza"""
    logger.info(f"🖼️ Conversione batch SVG→PNG: {dataset_file}")
    
    with open(dataset_file, 'r') as f:
        data = json.load(f)
    
    os.makedirs(output_dir, exist_ok=True)
    
    created_images = []
    total_batches = (len(data) + batch_size - 1) // batch_size
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(data))
        
        logger.info(f"   📦 Batch {batch_idx+1}/{total_batches}: esempi {start_idx}-{end_idx-1}")
        
        for i in range(start_idx, end_idx):
            example = data[i]
            
            # Converte SVG in XML completo
            svg_xml = de_parser_fast(example['xml'])
            
            # Path PNG
            png_file = os.path.join(output_dir, f"image_{i:04d}.png")
            
            # Conversione veloce
            success = svg_to_png_fast(svg_xml, png_file)
            
            if success:
                created_images.append({
                    'id': i,
                    'image_path': png_file,
                    'caption': example['caption'],
                    'filename': example.get('filename', f'image_{i:04d}')
                })
            else:
                logger.warning(f"⚠️ Conversione fallita per esempio {i}")
    
    logger.info(f"✅ Conversione completata: {len(created_images)}/{len(data)} immagini")
    return created_images

def run_blip2_fast(images_data, output_dir):
    """BLIP-2 ottimizzato"""
    logger.info("🔄 BLIP-2 evaluation...")
    
    results = []
    
    try:
        from transformers import Blip2Processor, Blip2ForConditionalGeneration
        from PIL import Image
        import torch
        
        # Carica modello
        processor = Blip2Processor.from_pretrained("Salesforce/blip2-opt-2.7b")
        model = Blip2ForConditionalGeneration.from_pretrained(
            "Salesforce/blip2-opt-2.7b",
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        # Batch processing
        batch_size = 8
        total_batches = (len(images_data) + batch_size - 1) // batch_size
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(images_data))
            
            if batch_idx % 10 == 0:
                logger.info(f"   📈 BLIP2 batch {batch_idx+1}/{total_batches}")
            
            batch_images = []
            batch_data = []
            
            # Carica batch di immagini
            for i in range(start_idx, end_idx):
                img_data = images_data[i]
                try:
                    image = Image.open(img_data['image_path']).convert('RGB')
                    batch_images.append(image)
                    batch_data.append(img_data)
                except Exception as e:
                    logger.warning(f"⚠️ Errore caricamento {img_data['image_path']}: {e}")
            
            # Processa batch
            if batch_images:
                try:
                    inputs = processor(batch_images, return_tensors="pt", padding=True)
                    inputs = {k: v.to(model.device) for k, v in inputs.items()}
                    
                    with torch.no_grad():
                        generated_ids = model.generate(**inputs, max_new_tokens=50, do_sample=False)
                    
                    predictions = processor.batch_decode(generated_ids, skip_special_tokens=True)
                    
                    # Salva risultati
                    for j, (img_data, prediction) in enumerate(zip(batch_data, predictions)):
                        results.append({
                            'id': img_data['id'],
                            'image_path': img_data['image_path'],
                            'ground_truth': img_data['caption'],
                            'prediction': prediction.strip(),
                            'model': 'BLIP2'
                        })
                        
                except Exception as e:
                    logger.error(f"❌ Errore batch BLIP2: {e}")
                    # Fallback individuale
                    for img_data in batch_data:
                        results.append({
                            'id': img_data['id'],
                            'image_path': img_data['image_path'],
                            'ground_truth': img_data['caption'],
                            'prediction': f"[BLIP2 Error: {str(e)[:50]}]",
                            'model': 'BLIP2'
                        })
    
    except Exception as e:
        logger.error(f"❌ BLIP2 setup fallito: {e}")
        # Fallback completo
        for img_data in images_data:
            results.append({
                'id': img_data['id'],
                'image_path': img_data['image_path'],
                'ground_truth': img_data['caption'],
                'prediction': f"[BLIP2 Setup Error: {str(e)[:50]}]",
                'model': 'BLIP2'
            })
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(output_dir, f"BLIP2_results_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ BLIP2 completato: {len(results)} risultati")
    return results_file

def run_placeholder_models(model_names, images_data, output_dir):
    """Placeholder per Florence2 e Idefics3"""
    results_files = []
    
    for model_name in model_names:
        logger.info(f"🔄 {model_name} (placeholder)...")
        
        results = []
        for img_data in images_data:
            results.append({
                'id': img_data['id'],
                'image_path': img_data['image_path'],
                'ground_truth': img_data['caption'],
                'prediction': f"[{model_name}] Generated caption for SVG image {img_data['id']}",
                'model': model_name
            })
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = os.path.join(output_dir, f"{model_name}_results_{timestamp}.json")
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        results_files.append(results_file)
        logger.info(f"✅ {model_name} completato")
    
    return results_files

def main():
    parser = argparse.ArgumentParser(description="Baseline evaluation veloce")
    parser.add_argument("--dataset_file", required=True)
    parser.add_argument("--output_dir", required=True)
    parser.add_argument("--max_examples", type=int, default=400)
    
    args = parser.parse_args()
    
    logger.info("🚀 BASELINE EVALUATION VELOCE")
    logger.info("=" * 50)
    
    os.makedirs(args.output_dir, exist_ok=True)
    images_dir = os.path.join(args.output_dir, "images")
    
    # Conversione SVG→PNG veloce
    images_data = create_images_batch(args.dataset_file, images_dir)
    
    if args.max_examples:
        images_data = images_data[:args.max_examples]
    
    logger.info(f"📊 Immagini create: {len(images_data)}")
    
    # BLIP-2 ottimizzato
    blip2_results = run_blip2_fast(images_data, args.output_dir)
    
    # Placeholder models
    placeholder_results = run_placeholder_models(['Florence2', 'Idefics3'], images_data, args.output_dir)
    
    logger.info("=" * 50)
    logger.info("🎉 BASELINE EVALUATION VELOCE COMPLETATA!")
    logger.info(f"📁 Output: {args.output_dir}")
    logger.info(f"🖼️ Immagini: {images_dir}")
    logger.info(f"📊 BLIP2: {blip2_results}")
    for pf in placeholder_results:
        logger.info(f"📊 Placeholder: {pf}")

if __name__ == "__main__":
    main()
