#!/usr/bin/env python3
"""
Script di test per valutare Gemma T9 con checkpoint esistente
Usa pochi esempi per testare il sistema di evaluation
"""

import subprocess
import sys
from pathlib import Path
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def find_latest_checkpoint():
    """Trova l'ultimo checkpoint disponibile"""
    checkpoint_dir = Path("experiments/xml_direct_input/outputs/gemma_t9_single_gpu_quantized")
    
    if not checkpoint_dir.exists():
        logger.error(f"❌ Directory checkpoint non trovata: {checkpoint_dir}")
        return None
    
    checkpoints = list(checkpoint_dir.glob("checkpoint-*"))
    if not checkpoints:
        logger.error("❌ Nessun checkpoint trovato")
        return None
    
    # Ordina per numero di step
    checkpoints.sort(key=lambda x: int(x.name.split('-')[1]))
    latest = checkpoints[-1]
    
    logger.info(f"✅ Checkpoint più recente: {latest}")
    return latest

def run_evaluation_pipeline(checkpoint_path, max_examples=50):
    """Esegue pipeline completa di evaluation"""
    
    # 1. Inference
    logger.info("🔄 Fase 1: Inference...")
    inference_cmd = [
        "python", "scripts/evaluation/run_trained_model_inference.py",
        "--base_model", "google/gemma-2-9b-it",
        "--checkpoint_path", str(checkpoint_path),
        "--model_name", "Gemma_T9_Test",
        "--test_file", "data/processed/xml_format_optimized/test_set_corrected_10k.json",
        "--max_examples", str(max_examples),
        "--output_dir", "evaluation_results/test"
    ]
    
    result = subprocess.run(inference_cmd, capture_output=True, text=True)
    if result.returncode != 0:
        logger.error(f"❌ Inference fallita: {result.stderr}")
        return False
    
    # Trova file risultati
    results_dir = Path("evaluation_results/test")
    results_files = list(results_dir.glob("Gemma_T9_Test_results_*.json"))
    if not results_files:
        logger.error("❌ File risultati non trovato")
        return False
    
    results_file = max(results_files, key=lambda x: x.stat().st_mtime)
    logger.info(f"✅ Inference completata: {results_file}")
    
    # 2. Calcolo metriche
    logger.info("🔄 Fase 2: Calcolo metriche...")
    metrics_cmd = [
        "python", "scripts/evaluation/calculate_trained_model_metrics.py",
        "--results_file", str(results_file),
        "--model_name", "Gemma_T9_Test",
        "--output_dir", "evaluation_results/test"
    ]
    
    result = subprocess.run(metrics_cmd, capture_output=True, text=True)
    if result.returncode != 0:
        logger.error(f"❌ Calcolo metriche fallito: {result.stderr}")
        return False
    
    # Trova file metriche
    metrics_files = list(results_dir.glob("Gemma_T9_Test_metrics_*.json"))
    if not metrics_files:
        logger.error("❌ File metriche non trovato")
        return False
    
    metrics_file = max(metrics_files, key=lambda x: x.stat().st_mtime)
    logger.info(f"✅ Metriche calcolate: {metrics_file}")
    
    # 3. Radar chart
    logger.info("🔄 Fase 3: Radar chart...")
    radar_cmd = [
        "python", "scripts/evaluation/create_trained_model_radar.py",
        "--trained_metrics", str(metrics_file),
        "--output_dir", "evaluation_results/test",
        "--output_name", "gemma_t9_test_radar"
    ]
    
    result = subprocess.run(radar_cmd, capture_output=True, text=True)
    if result.returncode != 0:
        logger.error(f"❌ Radar chart fallito: {result.stderr}")
        return False
    
    logger.info("✅ Radar chart creato")
    
    # Mostra risultati
    with open(metrics_file, 'r') as f:
        metrics_data = json.load(f)
    
    metrics = metrics_data.get('metrics', metrics_data)
    
    print("\n" + "="*50)
    print("📊 RISULTATI TEST EVALUATION")
    print("="*50)
    print(f"🔹 Checkpoint: {checkpoint_path}")
    print(f"🔹 Esempi testati: {max_examples}")
    print(f"🔹 Risultati: {results_file}")
    print(f"🔹 Metriche: {metrics_file}")
    print("\n📈 METRICHE CHIAVE:")
    
    key_metrics = ['bleu_4', 'meteor', 'cider', 'rouge_l', 'diversity']
    for metric in key_metrics:
        if metric in metrics:
            value = metrics[metric]
            if isinstance(value, float):
                print(f"  {metric.upper()}: {value:.4f}")
            else:
                print(f"  {metric.upper()}: {value}")
    
    print("="*50)
    
    return True

def main():
    logger.info("🚀 Test evaluation Gemma T9")
    
    # Trova checkpoint
    checkpoint_path = find_latest_checkpoint()
    if not checkpoint_path:
        sys.exit(1)
    
    # Esegui evaluation
    success = run_evaluation_pipeline(checkpoint_path, max_examples=50)
    
    if success:
        logger.info("✅ Test evaluation completato con successo!")
    else:
        logger.error("❌ Test evaluation fallito")
        sys.exit(1)

if __name__ == "__main__":
    main()
