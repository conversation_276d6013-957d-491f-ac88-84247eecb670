#!/usr/bin/env python3
"""
Calcola metriche per i risultati baseline FIXED di oggi (13 luglio 2025)
"""

import json
import numpy as np
import logging
from datetime import datetime
import os
import sys

# Importa librerie per calcolo metriche
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
import nltk
from pycocoevalcap.cider.cider import Cider
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

# Download NLTK data se necessario
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

def calculate_bleu_scores(reference, candidate):
    """Calcola BLEU-1, BLEU-2, BLEU-3, BLEU-4"""
    ref_tokens = reference.lower().split()
    cand_tokens = candidate.lower().split()

    smoothing = SmoothingFunction().method1

    bleu_1 = sentence_bleu([ref_tokens], cand_tokens, weights=(1, 0, 0, 0), smoothing_function=smoothing)
    bleu_2 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
    bleu_3 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
    bleu_4 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)

    return bleu_1, bleu_2, bleu_3, bleu_4

def calculate_meteor_score(reference, candidate):
    """Calcola METEOR score"""
    try:
        return meteor_score([reference.lower().split()], candidate.lower().split())
    except:
        return 0.0

def calculate_cider_score(references, candidates):
    """Calcola CIDEr score"""
    try:
        # Formato richiesto da pycocoevalcap
        gts = {}
        res = {}

        for i, (ref, cand) in enumerate(zip(references, candidates)):
            gts[i] = [ref]
            res[i] = [cand]

        cider = Cider()
        score, _ = cider.compute_score(gts, res)
        return score
    except Exception as e:
        logger.warning(f"⚠️ Errore CIDEr: {e}")
        return 0.0

def calculate_clip_score(references, candidates):
    """Calcola CLIPScore usando sentence transformers come proxy"""
    try:
        model = SentenceTransformer('all-MiniLM-L6-v2')

        ref_embeddings = model.encode(references)
        cand_embeddings = model.encode(candidates)

        similarities = []
        for ref_emb, cand_emb in zip(ref_embeddings, cand_embeddings):
            sim = cosine_similarity([ref_emb], [cand_emb])[0][0]
            similarities.append(sim)

        return np.mean(similarities) * 100  # Scala a 0-100
    except Exception as e:
        logger.warning(f"⚠️ Errore CLIPScore: {e}")
        return 0.0

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def calculate_metrics_for_file(result_file):
    """Calcola metriche per un file di risultati"""
    logger.info(f"📊 Calcolo metriche per: {result_file}")
    
    with open(result_file, 'r') as f:
        data = json.load(f)
    
    model_name = data.get("model", "Unknown")
    results = data.get("results", [])
    
    if not results:
        logger.warning(f"⚠️ Nessun risultato in {result_file}")
        return None
    
    # Estrai riferimenti e candidati
    references = []
    candidates = []
    
    for result in results:
        ref = result.get("ground_truth", "").strip()
        cand = result.get("generated_caption", "").strip()
        
        if ref and cand:
            references.append(ref)
            candidates.append(cand)
    
    if not references:
        logger.warning(f"⚠️ Nessuna coppia valida in {result_file}")
        return None
    
    logger.info(f"📈 Calcolo metriche per {len(references)} esempi...")
    
    # Calcola metriche BLEU e METEOR
    bleu_scores = []
    meteor_scores = []
    
    for ref, cand in zip(references, candidates):
        bleu_1, bleu_2, bleu_3, bleu_4 = calculate_bleu_scores(ref, cand)
        meteor = calculate_meteor_score(ref, cand)
        
        bleu_scores.append([bleu_1, bleu_2, bleu_3, bleu_4])
        meteor_scores.append(meteor)
    
    # Calcola medie
    avg_bleu = np.mean(bleu_scores, axis=0)
    avg_meteor = np.mean(meteor_scores)
    
    # Calcola CIDEr e CLIPScore
    cider_score = calculate_cider_score(references, candidates)
    clip_score = calculate_clip_score(references, candidates)
    
    # Crea oggetto metriche
    metrics = {
        "model": model_name,
        "total_examples": len(references),
        "bleu_1": float(avg_bleu[0]),
        "bleu_2": float(avg_bleu[1]),
        "bleu_3": float(avg_bleu[2]),
        "bleu_4": float(avg_bleu[3]),
        "meteor": float(avg_meteor),
        "cider": float(cider_score),
        "clip_score": float(clip_score),
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S")
    }
    
    logger.info(f"✅ {model_name}:")
    logger.info(f"   BLEU-1: {metrics['bleu_1']:.4f}")
    logger.info(f"   BLEU-2: {metrics['bleu_2']:.4f}")
    logger.info(f"   BLEU-3: {metrics['bleu_3']:.4f}")
    logger.info(f"   BLEU-4: {metrics['bleu_4']:.4f}")
    logger.info(f"   METEOR: {metrics['meteor']:.4f}")
    logger.info(f"   CIDEr: {metrics['cider']:.4f}")
    logger.info(f"   CLIPScore: {metrics['clip_score']:.4f}")
    
    return metrics

def main():
    print("🎯 CALCOLO METRICHE BASELINE FIXED")
    print("==================================")
    
    # File risultati FIXED di oggi
    result_files = [
        "evaluation_results/florence2_FIXED_results_20250713_102506.json",
        "evaluation_results/idefics3_FIXED_results_20250713_110649.json"
    ]
    
    all_metrics = {}
    
    for result_file in result_files:
        if not os.path.exists(result_file):
            logger.warning(f"⚠️ File non trovato: {result_file}")
            continue
        
        metrics = calculate_metrics_for_file(result_file)
        if metrics:
            all_metrics[metrics["model"]] = metrics
    
    if not all_metrics:
        logger.error("❌ Nessuna metrica calcolata!")
        return
    
    # Salva tutte le metriche
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"evaluation_results/baseline_metrics_FIXED_{timestamp}.json"
    
    with open(output_path, 'w') as f:
        json.dump(all_metrics, f, indent=2)
    
    logger.info(f"✅ Metriche salvate: {output_path}")
    
    # Stampa riepilogo
    print("\n" + "="*60)
    print("📊 RIEPILOGO METRICHE BASELINE FIXED")
    print("="*60)
    for model, metrics in all_metrics.items():
        print(f"\n🎯 {model} ({metrics['total_examples']} esempi):")
        print(f"   BLEU-1: {metrics['bleu_1']:.4f}")
        print(f"   BLEU-2: {metrics['bleu_2']:.4f}")
        print(f"   BLEU-3: {metrics['bleu_3']:.4f}")
        print(f"   BLEU-4: {metrics['bleu_4']:.4f}")
        print(f"   METEOR: {metrics['meteor']:.4f}")
        print(f"   CIDEr: {metrics['cider']:.4f}")
        print(f"   CLIPScore: {metrics['clip_score']:.4f}")
    
    return all_metrics

if __name__ == "__main__":
    main()
