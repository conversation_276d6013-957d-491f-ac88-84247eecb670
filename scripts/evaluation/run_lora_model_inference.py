#!/usr/bin/env python3
"""
Inference con modello LoRA (NON merged) - SOLUZIONE DEFINITIVA
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from peft import PeftModel
import json
import argparse
from datetime import datetime
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_lora_model(base_model_path, lora_checkpoint_path):
    """Carica modello base + LoRA adapter"""
    logger.info(f"🔧 Caricamento modello base: {base_model_path}")
    
    # Quantization config
    quantization_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_use_double_quant=True
    )
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(base_model_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load base model
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_path,
        quantization_config=quantization_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True
    )
    
    # Load LoRA adapter
    logger.info(f"🔧 Caricamento LoRA adapter: {lora_checkpoint_path}")
    model = PeftModel.from_pretrained(base_model, lora_checkpoint_path)
    
    logger.info(f"✅ Modello LoRA caricato con successo")
    return model, tokenizer

def run_inference(model, tokenizer, test_data, max_examples, model_name):
    """Esegue inference su test data"""
    logger.info(f"🔄 Inizio inference su {min(max_examples, len(test_data))} esempi...")
    
    results = []
    
    for i, example in enumerate(test_data[:max_examples]):
        if i % 10 == 0:
            logger.info(f"📈 Progresso: {i}/{max_examples} ({i/max_examples*100:.1f}%)")
        
        # Input prompt
        svg_input = example.get('xml', example.get('svg', example.get('input', '')))
        
        # Usa il prompt corretto per modelli instruct
        if "llama" in model_name.lower():
            # Llama instruct format (CORRETTO)
            prompt = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\nDescribe this SVG:\n{svg_input}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
        elif "gemma" in model_name.lower():
            # Gemma instruct format
            prompt = f"<start_of_turn>user\nDescribe this SVG:\n{svg_input}<end_of_turn>\n<start_of_turn>model\n"
        else:
            prompt = f"Generate a caption for this SVG: {svg_input}"
        
        # Tokenize
        inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
        inputs = {k: v.to(model.device) for k, v in inputs.items()}
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=150,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                pad_token_id=tokenizer.eos_token_id
            )
        
        # Decode solo la parte generata (nuovi token)
        input_length = inputs['input_ids'].shape[1]
        generated_tokens = outputs[0][input_length:]
        prediction = tokenizer.decode(generated_tokens, skip_special_tokens=True).strip()
        
        # Salva risultato
        result = {
            'id': i,
            'svg': svg_input,
            'ground_truth': example.get('caption', example.get('output', '')),
            'prediction': prediction,
            'model': model_name
        }
        results.append(result)
    
    logger.info(f"✅ Inference completata su {len(results)} esempi")
    return results

def main():
    parser = argparse.ArgumentParser(description="Inference con modello LoRA")
    parser.add_argument("--base_model_path", required=True, help="Path al modello base")
    parser.add_argument("--lora_checkpoint_path", required=True, help="Path al checkpoint LoRA")
    parser.add_argument("--model_name", required=True, help="Nome del modello")
    parser.add_argument("--test_file", required=True, help="File test JSON")
    parser.add_argument("--max_examples", type=int, default=100, help="Numero massimo esempi")
    parser.add_argument("--output_dir", required=True, help="Directory output")
    
    args = parser.parse_args()
    
    # Carica modello LoRA
    model, tokenizer = load_lora_model(args.base_model_path, args.lora_checkpoint_path)
    
    # Carica test data
    logger.info(f"📊 Caricamento dataset: {args.test_file}")
    with open(args.test_file, 'r') as f:
        test_data = json.load(f)
    logger.info(f"📊 Limitato a {args.max_examples} esempi")
    
    # Inference
    results = run_inference(model, tokenizer, test_data, args.max_examples, args.model_name)
    
    # Salva risultati
    os.makedirs(args.output_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(args.output_dir, f"{args.model_name}_results_{timestamp}.json")
    
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ Risultati salvati in: {output_file}")

if __name__ == "__main__":
    main()
