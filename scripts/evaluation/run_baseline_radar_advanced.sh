#!/bin/bash

echo "📊 GENERAZIONE RADAR CHART BASELINE AVANZATO"
echo "============================================="

# Configurazione
RESULTS_DIR="results/baseline_evaluation"
OUTPUT_DIR="results/baseline_charts"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Crea directory
mkdir -p "$OUTPUT_DIR"

# Attiva ambiente
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "📁 Results dir: $RESULTS_DIR"
echo "📁 Output dir: $OUTPUT_DIR"
echo "⏰ Timestamp: $TIMESTAMP"

# Genera radar chart avanzato
python scripts/evaluation/generate_baseline_radar_advanced.py \
    --results_dir "$RESULTS_DIR" \
    --output_dir "$OUTPUT_DIR"

echo ""
echo "🎉 RADAR CHART AVANZATO COMPLETATO!"
echo "📊 Controlla: $OUTPUT_DIR/"
echo "📊 Metriche: BLEU 1-4, METEOR, CIDEr, CLIPScore"
echo "📊 Scale: Appropriate per ogni metrica"
