#!/usr/bin/env python3
"""
Script per creare grafico radar confrontando modelli trained con baseline
"""

import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_metrics(metrics_file):
    """Carica metriche da file JSON"""
    with open(metrics_file, 'r') as f:
        data = json.load(f)
    
    if 'metrics' in data:
        return data['model_name'], data['metrics']
    else:
        # Formato legacy
        return Path(metrics_file).stem, data

def load_baseline_metrics():
    """Carica metriche baseline più recenti"""
    baseline_dir = Path("evaluation_results")
    
    # Cerca il file baseline più recente
    baseline_files = list(baseline_dir.glob("baseline_metrics_*.json"))
    if not baseline_files:
        logger.warning("⚠️ Nessun file baseline trovato, uso valori di default")
        return {
            "BLIP-2": {"bleu_4": 0.0506, "meteor": 0.2227, "cider": 0.4428, "rouge_l": 0.2363, "diversity": 99.25},
            "Florence2": {"bleu_4": 0.0169, "meteor": 0.3234, "cider": 0.2051, "rouge_l": 0.2053, "diversity": 95.0},
            "IDEFICS3": {"bleu_4": 0.0108, "meteor": 0.2208, "cider": 0.2486, "rouge_l": 0.1347, "diversity": 98.0}
        }
    
    # Usa il file più recente
    latest_baseline = max(baseline_files, key=lambda x: x.stat().st_mtime)
    logger.info(f"📊 Caricamento baseline da: {latest_baseline}")
    
    with open(latest_baseline, 'r') as f:
        baseline_data = json.load(f)
    
    return baseline_data

def normalize_metrics(metrics_dict, metric_names):
    """Normalizza metriche per il radar chart"""
    normalized = {}
    
    # Valori massimi per normalizzazione (basati su esperienza)
    max_values = {
        'bleu_4': 0.1,      # BLEU-4 raramente supera 0.1 per SVG
        'meteor': 0.4,      # METEOR può arrivare a 0.4
        'cider': 0.5,       # CIDEr può arrivare a 0.5
        'rouge_l': 0.3,     # ROUGE-L raramente supera 0.3
        'diversity': 100.0  # Diversity è in percentuale
    }
    
    for model, metrics in metrics_dict.items():
        normalized[model] = []
        for metric in metric_names:
            value = metrics.get(metric, 0)
            max_val = max_values.get(metric, 1.0)
            normalized_value = min(value / max_val, 1.0) * 100  # Scala 0-100
            normalized[model].append(normalized_value)
    
    return normalized

def create_radar_chart(metrics_dict, output_file):
    """Crea grafico radar"""
    # Metriche da visualizzare
    metric_names = ['bleu_4', 'meteor', 'cider', 'rouge_l', 'diversity']
    metric_labels = ['BLEU-4', 'METEOR', 'CIDEr', 'ROUGE-L', 'Diversity']
    
    # Normalizza metriche
    normalized_metrics = normalize_metrics(metrics_dict, metric_names)
    
    # Setup radar chart
    angles = np.linspace(0, 2 * np.pi, len(metric_names), endpoint=False).tolist()
    angles += angles[:1]  # Chiudi il cerchio
    
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    # Colori per i modelli
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
    
    # Plotta ogni modello
    for i, (model, values) in enumerate(normalized_metrics.items()):
        values += values[:1]  # Chiudi il cerchio
        ax.plot(angles, values, 'o-', linewidth=2, label=model, color=colors[i % len(colors)])
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
    
    # Personalizza grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metric_labels, fontsize=12)
    ax.set_ylim(0, 100)
    ax.set_yticks([20, 40, 60, 80, 100])
    ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'], fontsize=10)
    ax.grid(True)
    
    # Titolo e legenda
    plt.title('Confronto Modelli Trained vs Baseline\nMetriche di Captioning SVG', 
              fontsize=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=10)
    
    # Salva
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.savefig(output_file.with_suffix('.pdf'), bbox_inches='tight')
    
    logger.info(f"📊 Radar chart salvato: {output_file}")
    logger.info(f"📊 Radar chart PDF: {output_file.with_suffix('.pdf')}")

def main():
    parser = argparse.ArgumentParser(description="Crea radar chart per modelli trained")
    parser.add_argument("--trained_metrics", type=str, nargs='+', required=True, 
                       help="File JSON con metriche modelli trained")
    parser.add_argument("--output_dir", type=str, default="evaluation_results/trained_models")
    parser.add_argument("--output_name", type=str, default="trained_models_radar")
    args = parser.parse_args()
    
    # Setup
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    logger.info("🚀 Creazione radar chart per modelli trained")
    
    # Carica metriche baseline
    baseline_metrics = load_baseline_metrics()
    
    # Carica metriche modelli trained
    all_metrics = baseline_metrics.copy()
    
    for metrics_file in args.trained_metrics:
        model_name, metrics = load_metrics(metrics_file)
        all_metrics[model_name] = metrics
        logger.info(f"📊 Caricato: {model_name}")
    
    # Crea radar chart
    output_file = output_dir / f"{args.output_name}_{timestamp}.png"
    create_radar_chart(all_metrics, output_file)
    
    logger.info("✅ Radar chart creato!")
    
    # Mostra riassunto
    print("\n" + "="*60)
    print("📊 CONFRONTO MODELLI - METRICHE CHIAVE")
    print("="*60)
    
    key_metrics = ['bleu_4', 'meteor', 'cider', 'rouge_l', 'diversity']
    
    for model, metrics in all_metrics.items():
        print(f"\n🔹 {model}:")
        for metric in key_metrics:
            if metric in metrics:
                value = metrics[metric]
                if isinstance(value, float):
                    print(f"  {metric.upper()}: {value:.4f}")
                else:
                    print(f"  {metric.upper()}: {value}")
    
    print("\n" + "="*60)
    print(f"📁 Radar chart: {output_file}")

if __name__ == "__main__":
    main()
