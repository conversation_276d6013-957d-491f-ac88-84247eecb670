#!/bin/bash

echo "🧹 PULIZIA SCRIPT OBSOLETI - LEONARDO"
echo "Data: $(date)"
echo ""

cd /work/tesi_ediluzio

# Script SLURM obsoleti da rimuovere
echo "🗑️ RIMOZIONE SCRIPT SLURM OBSOLETI:"

# Lista script obsoleti
OBSOLETE_SCRIPTS=(
    "scripts/slurm/GEMMA_T9_RESUME_2250.slurm"
    "scripts/slurm/LLAMA_T8_RESUME_NORMAL_2603444.slurm"
    "scripts/slurm/LLAMA_T8_DEEPSPEED_SINGLE.slurm"
    "scripts/slurm/LLAMA_T8_DEEPSPEED_NO_MPI.slurm"
    "scripts/slurm/TEST_DEEPSPEED_SIMPLE.slurm"
    "scripts/slurm/GEMMA_T9_RESUME_4750.slurm"
)

for script in "${OBSOLETE_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo "  ❌ Rimozione: $script"
        rm "$script"
    else
        echo "  ✅ Non trovato: $script"
    fi
done

# Script training obsoleti
echo ""
echo "🗑️ RIMOZIONE SCRIPT TRAINING OBSOLETI:"

OBSOLETE_TRAINING=(
    "scripts/training/train_lora_deepspeed.py.OLD"
    "scripts/training/train_lora_multi_gpu_simple.py.BACKUP"
    "scripts/training/train_lora_single_gpu.py.OLD"
)

for script in "${OBSOLETE_TRAINING[@]}"; do
    if [ -f "$script" ]; then
        echo "  ❌ Rimozione: $script"
        rm "$script"
    else
        echo "  ✅ Non trovato: $script"
    fi
done

# Config obsolete
echo ""
echo "🗑️ RIMOZIONE CONFIG OBSOLETE:"

OBSOLETE_CONFIGS=(
    "experiments/xml_direct_input/configs/deepspeed_single_gpu.json"
    "experiments/xml_direct_input/configs/deepspeed_no_mpi.json"
    "experiments/xml_direct_input/configs/deepspeed_simple.json"
)

for config in "${OBSOLETE_CONFIGS[@]}"; do
    if [ -f "$config" ]; then
        echo "  ❌ Rimozione: $config"
        rm "$config"
    else
        echo "  ✅ Non trovato: $config"
    fi
done

# Log obsoleti (più vecchi di 3 giorni)
echo ""
echo "🗑️ RIMOZIONE LOG OBSOLETI (>3 giorni):"
find logs -name "*.out" -mtime +3 -type f | head -10 | while read file; do
    echo "  ❌ Rimozione log: $file"
    rm "$file"
done

find logs -name "*.err" -mtime +3 -type f | head -10 | while read file; do
    echo "  ❌ Rimozione log: $file"
    rm "$file"
done

echo ""
echo "✅ PULIZIA SCRIPT OBSOLETI COMPLETATA!"
echo "Data fine: $(date)"
