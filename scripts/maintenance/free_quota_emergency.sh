#!/bin/bash

echo "🚨 LIBERAZIONE QUOTA EMERGENZA - LEONARDO"
echo "Data: $(date)"
echo ""

cd /work/tesi_ediluzio

# Spazio iniziale
echo "📊 SPAZIO DISCO INIZIALE:"
df -h .
echo ""

# Rimuovi chunks obsoleti (2.3GB)
if [ -d "chunks" ]; then
    echo "🗑️ Rimozione chunks obsoleti (2.3GB)..."
    rm -rf chunks/
    echo "✅ Chunks rimossi!"
else
    echo "✅ Chunks già rimossi"
fi

# Rimuovi cache HuggingFace vecchie
echo "🧹 Pulizia cache HuggingFace..."
if [ -d ".cache/huggingface/hub" ]; then
    # Rimuovi file temporanei
    find .cache/huggingface -name "*.tmp" -delete 2>/dev/null || true
    find .cache/huggingface -name "*.lock" -delete 2>/dev/null || true
    find .cache/huggingface -name "*.incomplete" -delete 2>/dev/null || true
    echo "✅ Cache HuggingFace pulita"
fi

# Rimuovi log vecchi (>7 giorni)
echo "🗑️ Rimozione log vecchi..."
find logs -name "*.out" -mtime +7 -delete 2>/dev/null || true
find logs -name "*.err" -mtime +7 -delete 2>/dev/null || true
echo "✅ Log vecchi rimossi"

# Rimuovi run WandB vecchi
echo "🗑️ Pulizia WandB..."
if [ -d "wandb" ]; then
    find wandb -name "run-*" -type d | sort | head -n -5 | xargs rm -rf 2>/dev/null || true
    echo "✅ WandB pulito"
fi

# Spazio finale
echo ""
echo "📊 SPAZIO DISCO FINALE:"
df -h .
echo ""

echo "✅ LIBERAZIONE QUOTA COMPLETATA!"
echo "Data fine: $(date)"
