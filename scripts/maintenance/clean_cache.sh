#!/bin/bash

echo "🧹 PULIZIA CACHE SISTEMA - LEONARDO"
echo "Data: $(date)"
echo ""

# Spazio iniziale
echo "📊 SPAZIO DISCO INIZIALE:"
df -h /work/tesi_ediluzio
echo ""

# Cache HuggingFace
echo "🤗 CACHE HUGGINGFACE:"
if [ -d ".cache/huggingface" ]; then
    echo "Dimensione attuale: $(du -sh .cache/huggingface/ | cut -f1)"
    
    # Rimuovi file temporanei e lock
    echo "🧹 Rimozione file temporanei..."
    find .cache/huggingface -name "*.tmp" -delete 2>/dev/null || true
    find .cache/huggingface -name "*.lock" -delete 2>/dev/null || true
    find .cache/huggingface -name "*.incomplete" -delete 2>/dev/null || true
    
    echo "Dimensione dopo pulizia: $(du -sh .cache/huggingface/ | cut -f1)"
else
    echo "Cache HuggingFace non trovata"
fi
echo ""

# Cache PyTorch
echo "🔥 CACHE PYTORCH:"
if [ -d "$HOME/.cache/torch" ]; then
    echo "Dimensione: $(du -sh $HOME/.cache/torch | cut -f1)"
    echo "🧹 Pulizia cache PyTorch..."
    rm -rf $HOME/.cache/torch/hub/checkpoints/* 2>/dev/null || true
    echo "Cache PyTorch pulita"
else
    echo "Cache PyTorch non trovata"
fi
echo ""

# Cache Pip
echo "📦 CACHE PIP:"
if [ -d "$HOME/.cache/pip" ]; then
    echo "Dimensione: $(du -sh $HOME/.cache/pip | cut -f1)"
    echo "🧹 Pulizia cache Pip..."
    pip cache purge 2>/dev/null || true
    echo "Cache Pip pulita"
else
    echo "Cache Pip non trovata"
fi
echo ""

# Wandb cache
echo "📊 CACHE WANDB:"
if [ -d "wandb" ]; then
    echo "Dimensione wandb/: $(du -sh wandb/ | cut -f1)"
    
    # Rimuovi run vecchi (mantieni ultimi 10)
    echo "🧹 Pulizia run WandB vecchi..."
    find wandb -name "run-*" -type d | sort | head -n -10 | xargs rm -rf 2>/dev/null || true
    
    echo "Dimensione dopo pulizia: $(du -sh wandb/ | cut -f1)"
else
    echo "Directory wandb non trovata"
fi
echo ""

# Log vecchi
echo "📝 LOG VECCHI:"
if [ -d "logs" ]; then
    echo "Dimensione logs/: $(du -sh logs/ | cut -f1)"
    
    # Rimuovi log più vecchi di 7 giorni
    echo "🧹 Rimozione log > 7 giorni..."
    find logs -name "*.out" -mtime +7 -delete 2>/dev/null || true
    find logs -name "*.err" -mtime +7 -delete 2>/dev/null || true
    
    echo "Dimensione dopo pulizia: $(du -sh logs/ | cut -f1)"
else
    echo "Directory logs non trovata"
fi
echo ""

# Spazio finale
echo "📊 SPAZIO DISCO FINALE:"
df -h /work/tesi_ediluzio
echo ""

echo "✅ PULIZIA COMPLETATA!"
echo "Data fine: $(date)"
