#!/bin/bash

echo "🧹 PULIZIA CHECKPOINT SICURA - LEONARDO"
echo "Regola: Mantieni solo gli ultimi 2 checkpoint per modello"
echo "Data: $(date)"
echo ""

cd /work/tesi_ediluzio

# Funzione per pulire checkpoint di una directory
clean_checkpoints() {
    local model_dir="$1"
    local model_name="$2"
    
    echo "🔍 Analisi checkpoint per $model_name:"
    echo "   Directory: $model_dir"
    
    if [ ! -d "$model_dir" ]; then
        echo "   ❌ Directory non trovata"
        return
    fi
    
    # Lista checkpoint ordinati per numero
    checkpoints=($(ls -d $model_dir/checkpoint-* 2>/dev/null | sort -V))
    total_checkpoints=${#checkpoints[@]}
    
    echo "   📊 Checkpoint trovati: $total_checkpoints"
    
    if [ $total_checkpoints -le 2 ]; then
        echo "   ✅ Nessuna pulizia necessaria (≤2 checkpoint)"
        return
    fi
    
    # Calcola quanti checkpoint rimuovere
    to_remove=$((total_checkpoints - 2))
    echo "   🗑️ Checkpoint da rimuovere: $to_remove"
    
    # Rimuovi i checkpoint più vecchi
    for ((i=0; i<to_remove; i++)); do
        checkpoint_path="${checkpoints[i]}"
        checkpoint_name=$(basename "$checkpoint_path")
        
        echo "   ❌ Rimozione: $checkpoint_name"
        rm -rf "$checkpoint_path"
    done
    
    # Mostra checkpoint rimanenti
    remaining_checkpoints=($(ls -d $model_dir/checkpoint-* 2>/dev/null | sort -V))
    echo "   ✅ Checkpoint mantenuti:"
    for checkpoint in "${remaining_checkpoints[@]}"; do
        checkpoint_name=$(basename "$checkpoint")
        size=$(du -sh "$checkpoint" | cut -f1)
        echo "      - $checkpoint_name ($size)"
    done
    
    echo ""
}

# Pulizia checkpoint per ogni modello
echo "🧹 PULIZIA CHECKPOINT PER MODELLO:"
echo ""

# GEMMA T9
clean_checkpoints "experiments/xml_direct_input/outputs/gemma_t9_fixed_leonardo" "GEMMA T9"

# LLAMA T8
clean_checkpoints "experiments/xml_direct_input/outputs/llama_t8_fixed_leonardo" "LLAMA T8"

# GEMMA T9 DeepSpeed (se esiste)
clean_checkpoints "experiments/xml_direct_input/outputs/gemma_t9_deepspeed_prod" "GEMMA T9 DeepSpeed"

# LLAMA T8 DeepSpeed (se esiste)
clean_checkpoints "experiments/xml_direct_input/outputs/llama_t8_deepspeed_prod" "LLAMA T8 DeepSpeed"

# Calcola spazio liberato
echo "📊 SPAZIO DISCO DOPO PULIZIA:"
df -h /work/tesi_ediluzio

echo ""
echo "✅ PULIZIA CHECKPOINT COMPLETATA!"
echo "Data fine: $(date)"
