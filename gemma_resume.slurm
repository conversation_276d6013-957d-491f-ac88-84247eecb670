#!/bin/bash
#SBATCH --job-name=GEMMA_RESUME
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/GEMMA_RESUME_%j.out
#SBATCH --error=logs/GEMMA_RESUME_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 GEMMA T9 RESUME DA CHECKPOINT-1750"
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Fix PEFT bug
python -c "
import os
import site
trainer_path = os.path.join(site.getsitepackages()[0], 'transformers', 'trainer.py')
if os.path.exists(trainer_path):
    with open(trainer_path, 'r') as f:
        content = f.read()
    if 'if len(active_adapters) > 1:' in content:
        content = content.replace(
            'if len(active_adapters) > 1:',
            'if hasattr(active_adapters, \"__len__\") and len(active_adapters) > 1:'
        )
        with open(trainer_path, 'w') as f:
            f.write(content)
        print('✅ Fix PEFT applicato')
    else:
        print('✅ Fix PEFT già presente o non necessario')
else:
    print('⚠️ trainer.py non trovato')
"

export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma_t9_resume_1750
export TOKENIZERS_PARALLELISM=false
export HUGGINGFACE_HUB_TOKEN=*************************************
export HUGGINGFACE_HUB_CACHE=/work/tesi_ediluzio/.cache/huggingface
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_single_gpu_quantized"
RESUME_FROM="$OUTPUT_DIR/checkpoint-1750"

python scripts/training/train_lora_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_single_gpu_quantized.json \
    --output_dir "$OUTPUT_DIR" \
    --resume_from_checkpoint "$RESUME_FROM" \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name gemma_t9_resume_1750
