
    <!DOCTYPE html>
    <html>
    <head>
        <title>SVG Captioning - Baseline Evaluation Report</title>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background-color: #f8f9fa; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 40px; border-bottom: 3px solid #007bff; padding-bottom: 20px; }
            .header h1 { color: #007bff; margin-bottom: 10px; }
            .metrics-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            .metrics-table th, .metrics-table td { border: 1px solid #dee2e6; padding: 12px; text-align: center; }
            .metrics-table th { background-color: #007bff; color: white; font-weight: bold; }
            .metrics-table tr:nth-child(even) { background-color: #f8f9fa; }
            .radar-section { text-align: center; margin: 40px 0; }
            .radar-section img { max-width: 100%; height: auto; border: 1px solid #dee2e6; border-radius: 8px; }
            .examples-section { margin-top: 40px; }
            .example { margin: 20px 0; padding: 20px; border: 1px solid #dee2e6; border-radius: 8px; background-color: #f8f9fa; }
            .model-name { font-weight: bold; color: #007bff; font-size: 14px; }
            .prediction { background-color: #e3f2fd; padding: 12px; margin: 8px 0; border-radius: 5px; border-left: 4px solid #2196f3; }
            .ground-truth { background-color: #e8f5e8; padding: 12px; margin: 8px 0; border-radius: 5px; border-left: 4px solid #4caf50; }
            .metric-highlight { background-color: #fff3cd; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎯 SVG Captioning - Baseline Evaluation Report</h1>
                <p><strong>Generated:</strong> 2025-07-20 13:06:35</p>
                <p><strong>Dataset:</strong> 400 SVG examples with RGB colors</p>
                <p><strong>Models:</strong> BLIP-2, Florence2, Idefics3</p>
            </div>
            
            <h2>📊 Performance Metrics</h2>
            <table class="metrics-table">
                <tr>
                    <th>Model</th>
                    <th>BLEU-1</th>
                    <th>BLEU-2</th>
                    <th>BLEU-3</th>
                    <th>BLEU-4</th>
                    <th>ROUGE-1</th>
                    <th>ROUGE-L</th>
                    <th>Semantic Sim</th>
                    <th>Examples</th>
                </tr>
    
            <tr>
                <td class="model-name">Idefics3</td>
                <td>0.012</td>
                <td>0.005</td>
                <td>0.004</td>
                <td>0.003</td>
                <td>0.040</td>
                <td>0.038</td>
                <td class="metric-highlight">0.250</td>
                <td>400</td>
            </tr>
        
            <tr>
                <td class="model-name">Florence2</td>
                <td>0.012</td>
                <td>0.005</td>
                <td>0.004</td>
                <td>0.003</td>
                <td>0.040</td>
                <td>0.038</td>
                <td class="metric-highlight">0.250</td>
                <td>400</td>
            </tr>
        
            <tr>
                <td class="model-name">BLIP2</td>
                <td>0.014</td>
                <td>0.006</td>
                <td>0.004</td>
                <td>0.003</td>
                <td>0.097</td>
                <td>0.088</td>
                <td class="metric-highlight">0.250</td>
                <td>400</td>
            </tr>
        
        </table>
        
        <div class="radar-section">
            <h2>📈 Performance Radar Chart</h2>
            <img src="baseline_radar_chart_20250720_130633.png" alt="Performance Radar Chart">
            <p><em>Radar chart showing normalized performance metrics (0-1 scale)</em></p>
        </div>
        
        <div class="examples-section">
            <h2>📝 Example Predictions</h2>
    <div class="example"><h3>Example 1</h3><div class="ground-truth"><strong>Ground Truth:</strong> The image depicts a simple, minimalist sign with a black border and a white background. The sign is rectangular in shape and has a vertical orientation. Inside the rectangle, there is a single letter ...</div><div class="prediction"><strong>Idefics3:</strong> [Idefics3] Generated caption for SVG image 0</div><div class="prediction"><strong>Florence2:</strong> [Florence2] Generated caption for SVG image 0</div><div class="prediction"><strong>BLIP2:</strong> a white paper with the words sdscc</div></div><div class="example"><h3>Example 2</h3><div class="ground-truth"><strong>Ground Truth:</strong> The image depicts a simple, stylized chemical structure of a molecule. The molecule appears to be a single atom or a small molecule, characterized by a central atom connected to two other atoms. The c...</div><div class="prediction"><strong>Idefics3:</strong> [Idefics3] Generated caption for SVG image 1</div><div class="prediction"><strong>Florence2:</strong> [Florence2] Generated caption for SVG image 1</div><div class="prediction"><strong>BLIP2:</strong> the cover page for the document is shown</div></div><div class="example"><h3>Example 3</h3><div class="ground-truth"><strong>Ground Truth:</strong> The image depicts the image contains a handwritten signature. The signature is written in a cursive style, with a flowing, connected script. The signature is placed on a white background, and it is th...</div><div class="prediction"><strong>Idefics3:</strong> [Idefics3] Generated caption for SVG image 2</div><div class="prediction"><strong>Florence2:</strong> [Florence2] Generated caption for SVG image 2</div><div class="prediction"><strong>BLIP2:</strong> a white paper with the words ssc and ssc</div></div><div class="example"><h3>Example 4</h3><div class="ground-truth"><strong>Ground Truth:</strong> The image depicts a simple, single-colored circular shape with a green hue. The circle is a solid, continuous form without any breaks or interruptions. The color is a vibrant green, which is consisten...</div><div class="prediction"><strong>Idefics3:</strong> [Idefics3] Generated caption for SVG image 3</div><div class="prediction"><strong>Florence2:</strong> [Florence2] Generated caption for SVG image 3</div><div class="prediction"><strong>BLIP2:</strong> a white paper with the words sds commission</div></div><div class="example"><h3>Example 5</h3><div class="ground-truth"><strong>Ground Truth:</strong> The image depicts a simple, cartoon-like illustration of a passport or ID card. The card is rectangular in shape and has a black border. The top section of the card features a small, circular slot, li...</div><div class="prediction"><strong>Idefics3:</strong> [Idefics3] Generated caption for SVG image 4</div><div class="prediction"><strong>Florence2:</strong> [Florence2] Generated caption for SVG image 4</div><div class="prediction"><strong>BLIP2:</strong> the cover page for the document is shown</div></div><div class="example"><h3>Example 6</h3><div class="ground-truth"><strong>Ground Truth:</strong> The image depicts a single black letter "A" in a bold, uppercase font. The letter "A" is positioned in the center of the image, occupying a significant portion of the frame. The letter is black and ap...</div><div class="prediction"><strong>Idefics3:</strong> [Idefics3] Generated caption for SVG image 5</div><div class="prediction"><strong>Florence2:</strong> [Florence2] Generated caption for SVG image 5</div><div class="prediction"><strong>BLIP2:</strong> a white paper with the words ssc and ssc</div></div><div class="example"><h3>Example 7</h3><div class="ground-truth"><strong>Ground Truth:</strong> The image depicts a simple, line-drawn illustration of a lollipop. The lollipop is characterized by its circular shape, which is divided into concentric circles, creating a spiral pattern. The outermo...</div><div class="prediction"><strong>Idefics3:</strong> [Idefics3] Generated caption for SVG image 6</div><div class="prediction"><strong>Florence2:</strong> [Florence2] Generated caption for SVG image 6</div><div class="prediction"><strong>BLIP2:</strong> a white paper with the words ssc and ssc</div></div><div class="example"><h3>Example 8</h3><div class="ground-truth"><strong>Ground Truth:</strong> The image depicts a black square with a white border. Inside the square, there is a white object that resembles a stylized letter "L" or a stylized lowercase "i" with a rounded top. The object is posi...</div><div class="prediction"><strong>Idefics3:</strong> [Idefics3] Generated caption for SVG image 7</div><div class="prediction"><strong>Florence2:</strong> [Florence2] Generated caption for SVG image 7</div><div class="prediction"><strong>BLIP2:</strong> a white paper with the words sdscc</div></div><div class="example"><h3>Example 9</h3><div class="ground-truth"><strong>Ground Truth:</strong> The image depicts a simple, cartoon-style illustration of a penguin. The penguin is standing upright with its body facing forward. The penguin has a round, black body with a white belly, which is a co...</div><div class="prediction"><strong>Idefics3:</strong> [Idefics3] Generated caption for SVG image 8</div><div class="prediction"><strong>Florence2:</strong> [Florence2] Generated caption for SVG image 8</div><div class="prediction"><strong>BLIP2:</strong> a white paper with the words ssc and ssc</div></div><div class="example"><h3>Example 10</h3><div class="ground-truth"><strong>Ground Truth:</strong> The image depicts a simple, black, crescent moon shape. The moon is positioned in the center of the image, and it is oriented with its curved side facing upwards. The moon's shape is symmetrical, with...</div><div class="prediction"><strong>Idefics3:</strong> [Idefics3] Generated caption for SVG image 9</div><div class="prediction"><strong>Florence2:</strong> [Florence2] Generated caption for SVG image 9</div><div class="prediction"><strong>BLIP2:</strong> a white paper with the words sdscc</div></div>
            </div>
            
            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; text-align: center; color: #6c757d;">
                <p>Generated by SVG Captioning Evaluation System</p>
            </div>
        </div>
    </body>
    </html>
    