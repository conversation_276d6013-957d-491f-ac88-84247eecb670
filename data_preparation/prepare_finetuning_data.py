#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per la preparazione dei dati di fine-tuning.
Formatta i dati in diversi formati per il fine-tuning di modelli di linguaggio.
"""

import os
import json
import argparse
import logging
import random
from typing import Dict, List, Any, Optional
from tqdm import tqdm

from experiments.xml_direct_input.prompt_templates import (
    FINE_TUNING_TEMPLATES,
    format_prompt
)
from shared.svg_core.custom_tokenizer_utils import tokenize_svg

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_json(file_path: str) -> List[Dict[str, Any]]:
    """Carica un file JSON."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_jsonl(data: List[Dict[str, Any]], file_path: str) -> None:
    """<PERSON>va i dati in formato JSONL."""
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def prepare_data(
    dataset_path: str,
    output_dir: str,
    template_name: str = "instruction",
    use_custom_tokenizer: bool = False,
    max_samples: Optional[int] = None,
    random_seed: int = 42
) -> None:
    """
    Prepara i dati per il fine-tuning.
    
    Args:
        dataset_path: Percorso al file JSON del dataset
        output_dir: Directory di output per i dati formattati
        template_name: Nome del template da utilizzare
        use_custom_tokenizer: Se True, utilizza il tokenizer custom per SVG
        max_samples: Numero massimo di campioni da utilizzare (opzionale)
        random_seed: Seed per la selezione casuale dei campioni
    """
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)
    
    # Carica il dataset
    logger.info(f"Caricamento del dataset: {dataset_path}")
    dataset = load_json(dataset_path)
    logger.info(f"Caricati {len(dataset)} esempi.")
    
    # Seleziona un sottoinsieme casuale se specificato
    if max_samples and max_samples < len(dataset):
        random.seed(random_seed)
        dataset = random.sample(dataset, max_samples)
        logger.info(f"Selezionati {max_samples} esempi casuali.")
    
    # Verifica che il template esista
    if template_name not in FINE_TUNING_TEMPLATES:
        raise ValueError(f"Template non valido: {template_name}. Opzioni disponibili: {list(FINE_TUNING_TEMPLATES.keys())}")
    
    template = FINE_TUNING_TEMPLATES[template_name]
    logger.info(f"Utilizzo del template: {template_name}")
    
    # Prepara i dati
    formatted_data = []
    
    for example in tqdm(dataset, desc="Formattazione dati"):
        svg_data = example.get("xml", "")
        caption = example.get("caption", "")
        
        if not svg_data or not caption:
            continue
        
        # Tokenizza l'SVG se richiesto
        if use_custom_tokenizer:
            svg_data = tokenize_svg(svg_data)
        
        # Formatta il prompt
        prompt = format_prompt(template, svg_data, caption)
        
        # Aggiungi all'output
        formatted_data.append({
            "text": prompt
        })
    
    # Salva i dati formattati
    output_path = os.path.join(output_dir, f"finetuning_{template_name}{'_tokenized' if use_custom_tokenizer else ''}.jsonl")
    save_jsonl(formatted_data, output_path)
    logger.info(f"Dati formattati salvati in: {output_path}")
    
    # Crea anche una versione di test più piccola
    test_size = min(100, len(formatted_data))
    test_data = random.sample(formatted_data, test_size)
    test_path = os.path.join(output_dir, f"finetuning_{template_name}{'_tokenized' if use_custom_tokenizer else ''}_test.jsonl")
    save_jsonl(test_data, test_path)
    logger.info(f"Dati di test salvati in: {test_path}")

def main():
    parser = argparse.ArgumentParser(description="Prepara i dati per il fine-tuning.")
    parser.add_argument("--dataset_path", type=str, default="/work/tesi_ediluzio/data/processed/train_set_final.json", help="Percorso al file JSON del dataset.")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/data/processed/finetuning", help="Directory di output per i dati formattati.")
    parser.add_argument("--template", type=str, choices=list(FINE_TUNING_TEMPLATES.keys()), default="instruction", help="Template da utilizzare.")
    parser.add_argument("--use_custom_tokenizer", action="store_true", help="Utilizza il tokenizer custom per SVG.")
    parser.add_argument("--max_samples", type=int, default=None, help="Numero massimo di campioni da utilizzare (opzionale).")
    parser.add_argument("--random_seed", type=int, default=42, help="Seed per la selezione casuale dei campioni.")
    
    args = parser.parse_args()
    prepare_data(args.dataset_path, args.output_dir, args.template, args.use_custom_tokenizer, args.max_samples, args.random_seed)

if __name__ == "__main__":
    main()
