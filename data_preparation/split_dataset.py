import json
import random
import argparse
import os
from collections import defaultdict

def split_dataset(input_json_path, train_output_path, test_output_path, test_size=1000, seed=42, stratify_key="source"):
    """
    Splits a JSON dataset into training and testing sets.

    Args:
        input_json_path (str): Path to the input JSON file (list of dicts).
        train_output_path (str): Path to save the training set JSON file.
        test_output_path (str): Path to save the test set JSON file.
        test_size (int): The desired number of samples in the test set.
        seed (int): Random seed for reproducibility.
        stratify_key (str, optional): The key in the dictionary to use for stratification.
                                      Set to None to perform simple random sampling. Defaults to "source".
    """
    print(f"Loading dataset from: {input_json_path}")
    try:
        with open(input_json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"Error: Input file not found at {input_json_path}")
        return
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {input_json_path}")
        return

    if not isinstance(data, list):
        print("Error: Input JSON is not a list of objects.")
        return

    total_samples = len(data)
    print(f"Loaded {total_samples} total samples.")

    if test_size >= total_samples or test_size <= 0:
        print(f"Error: Invalid test_size ({test_size}). Must be between 1 and {total_samples - 1}.")
        return

    random.seed(seed) # Set seed for reproducibility

    test_indices = set()
    train_indices = set(range(total_samples))

    # Check if stratification is possible and requested
    can_stratify = False
    if stratify_key:
        if data and isinstance(data[0], dict) and stratify_key in data[0]:
            can_stratify = True
            print(f"Attempting stratified sampling based on key: '{stratify_key}'")
        else:
            print(f"Warning: Cannot stratify. Key '{stratify_key}' not found in data or data is empty/invalid. Using simple random sampling.")

    if can_stratify:
        # Stratified sampling
        groups = defaultdict(list)
        for i, item in enumerate(data):
            groups[item.get(stratify_key, "N/A")].append(i) # Group indices by stratify_key value

        test_fraction = test_size / total_samples

        for group_key, indices in groups.items():
            group_size = len(indices)
            num_test_in_group = max(1, round(group_size * test_fraction)) # Ensure at least 1 if group is small
            num_test_in_group = min(num_test_in_group, group_size) # Cannot take more than available

            # Randomly sample indices from this group for the test set
            sampled_indices = random.sample(indices, num_test_in_group)
            test_indices.update(sampled_indices)

        # Adjust total test set size if rounding caused deviations
        current_test_size = len(test_indices)
        if current_test_size != test_size:
             print(f"Adjusting test set size from {current_test_size} to {test_size} due to stratification rounding.")
             # Simple add/remove strategy for minor adjustments
             if current_test_size < test_size:
                  # Add more random samples from remaining train indices
                  needed = test_size - current_test_size
                  available_indices = list(train_indices - test_indices)
                  if len(available_indices) >= needed:
                       additional_indices = random.sample(available_indices, needed)
                       test_indices.update(additional_indices)
                  else: # Should not happen if test_size is valid
                       print("Warning: Not enough samples to reach exact test_size after stratification adjustment.")
             elif current_test_size > test_size:
                  # Remove random samples from current test set
                  to_remove_count = current_test_size - test_size
                  indices_to_remove = random.sample(list(test_indices), to_remove_count)
                  test_indices.difference_update(indices_to_remove)

        train_indices = train_indices - test_indices
        print(f"Stratified sampling complete. Test set size: {len(test_indices)}, Train set size: {len(train_indices)}")

    else:
        # Simple random sampling
        print("Performing simple random sampling.")
        test_indices = set(random.sample(range(total_samples), test_size))
        train_indices = set(range(total_samples)) - test_indices
        print(f"Simple random sampling complete. Test set size: {len(test_indices)}, Train set size: {len(train_indices)}")

    # Create train and test lists
    train_set = [data[i] for i in sorted(list(train_indices))]
    test_set = [data[i] for i in sorted(list(test_indices))]

    # Save the sets
    try:
        print(f"Saving training set ({len(train_set)} samples) to: {train_output_path}")
        os.makedirs(os.path.dirname(train_output_path), exist_ok=True)
        with open(train_output_path, 'w', encoding='utf-8') as f:
            json.dump(train_set, f, indent=2, ensure_ascii=False)

        print(f"Saving test set ({len(test_set)} samples) to: {test_output_path}")
        os.makedirs(os.path.dirname(test_output_path), exist_ok=True)
        with open(test_output_path, 'w', encoding='utf-8') as f:
            json.dump(test_set, f, indent=2, ensure_ascii=False)

        print("Dataset split completed successfully.")

    except Exception as e:
        print(f"Error saving output files: {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Split a JSON dataset into training and test sets.")
    parser.add_argument("input_json", help="Path to the input JSON dataset file.")
    parser.add_argument("train_output", help="Path to save the output training JSON file.")
    parser.add_argument("test_output", help="Path to save the output test JSON file.")
    parser.add_argument("--test_size", type=int, default=1000, help="Number of samples for the test set (default: 1000).")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducibility (default: 42).")
    parser.add_argument("--stratify_key", type=str, default="source", help="Key to stratify on (e.g., 'source'). Set to 'None' or empty string for simple random sampling.")

    args = parser.parse_args()

    strat_key = args.stratify_key if args.stratify_key and args.stratify_key.lower() != 'none' else None

    split_dataset(
        input_json_path=args.input_json,
        train_output_path=args.train_output,
        test_output_path=args.test_output,
        test_size=args.test_size,
        seed=args.seed,
        stratify_key=strat_key
    )