#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per l'analisi degli SVG nel dataset.
Estrae statistiche come numero di path, colori, dimensioni, ecc.
"""

import os
import json
import argparse
import logging
from typing import Dict, List, Any, Counter
import re
from collections import defaultdict
import xml.etree.ElementTree as ET
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from tqdm import tqdm

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Carica un file JSONL."""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line))
    return data

def load_json(file_path: str) -> List[Dict[str, Any]]:
    """Carica un file JSON."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def count_svg_elements(svg_xml: str) -> Dict[str, int]:
    """
    Conta gli elementi in un SVG.
    
    Args:
        svg_xml: Stringa XML dell'SVG
        
    Returns:
        Dizionario con il conteggio degli elementi
    """
    try:
        # Rimuovi il namespace per semplificare il parsing
        svg_xml = re.sub(r'xmlns="[^"]+"', '', svg_xml)
        
        # Parsa l'XML
        root = ET.fromstring(svg_xml)
        
        # Conta gli elementi
        element_counts = defaultdict(int)
        
        # Conta gli elementi diretti
        for elem in root.findall(".//*"):
            tag = elem.tag.split("}")[-1]  # Rimuovi il namespace se presente
            element_counts[tag] += 1
        
        return dict(element_counts)
    except Exception as e:
        logger.warning(f"Errore nel parsing dell'SVG: {e}")
        return {}

def extract_colors(svg_xml: str) -> List[str]:
    """
    Estrae i colori da un SVG.
    
    Args:
        svg_xml: Stringa XML dell'SVG
        
    Returns:
        Lista di colori
    """
    # Cerca attributi di colore (fill, stroke)
    fill_pattern = r'fill="([^"]+)"'
    stroke_pattern = r'stroke="([^"]+)"'
    
    fill_colors = re.findall(fill_pattern, svg_xml)
    stroke_colors = re.findall(stroke_pattern, svg_xml)
    
    # Unisci e rimuovi duplicati
    all_colors = list(set(fill_colors + stroke_colors))
    
    # Filtra colori non validi o 'none'
    valid_colors = [c for c in all_colors if c.lower() != 'none']
    
    return valid_colors

def extract_path_complexity(svg_xml: str) -> Dict[str, Any]:
    """
    Estrae la complessità dei path in un SVG.
    
    Args:
        svg_xml: Stringa XML dell'SVG
        
    Returns:
        Dizionario con statistiche sulla complessità dei path
    """
    # Cerca i path
    path_pattern = r'<path[^>]*d="([^"]+)"[^>]*>'
    paths = re.findall(path_pattern, svg_xml)
    
    if not paths:
        return {
            "num_paths": 0,
            "avg_commands": 0,
            "max_commands": 0,
            "min_commands": 0
        }
    
    # Conta i comandi in ciascun path
    command_counts = []
    for path in paths:
        # Conta i comandi (M, L, C, Z, ecc.)
        commands = re.findall(r'[A-Za-z]', path)
        command_counts.append(len(commands))
    
    return {
        "num_paths": len(paths),
        "avg_commands": sum(command_counts) / len(command_counts),
        "max_commands": max(command_counts),
        "min_commands": min(command_counts)
    }

def extract_svg_dimensions(svg_xml: str) -> Dict[str, Any]:
    """
    Estrae le dimensioni di un SVG.
    
    Args:
        svg_xml: Stringa XML dell'SVG
        
    Returns:
        Dizionario con le dimensioni
    """
    # Cerca attributi width e height
    width_pattern = r'width="([^"]+)"'
    height_pattern = r'height="([^"]+)"'
    viewbox_pattern = r'viewBox="([^"]+)"'
    
    width_match = re.search(width_pattern, svg_xml)
    height_match = re.search(height_pattern, svg_xml)
    viewbox_match = re.search(viewbox_pattern, svg_xml)
    
    width = width_match.group(1) if width_match else None
    height = height_match.group(1) if height_match else None
    viewbox = viewbox_match.group(1) if viewbox_match else None
    
    # Converti in numeri se possibile
    try:
        width = float(width) if width and width.replace('.', '', 1).isdigit() else width
    except:
        pass
    
    try:
        height = float(height) if height and height.replace('.', '', 1).isdigit() else height
    except:
        pass
    
    return {
        "width": width,
        "height": height,
        "viewbox": viewbox
    }

def analyze_dataset(dataset_path: str, output_dir: str) -> None:
    """
    Analizza un dataset di SVG.
    
    Args:
        dataset_path: Percorso al file JSON/JSONL del dataset
        output_dir: Directory di output per i risultati
    """
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)
    
    # Carica il dataset
    logger.info(f"Caricamento del dataset: {dataset_path}")
    if dataset_path.endswith('.jsonl'):
        dataset = load_jsonl(dataset_path)
    else:
        dataset = load_json(dataset_path)
    logger.info(f"Caricati {len(dataset)} esempi.")
    
    # Inizializza i contatori e le liste per le statistiche
    element_counts = Counter()
    color_counts = Counter()
    path_stats = {
        "num_paths": [],
        "avg_commands": [],
        "max_commands": [],
        "min_commands": []
    }
    dimensions = {
        "width": [],
        "height": []
    }
    
    # Analizza ogni SVG
    for example in tqdm(dataset, desc="Analisi SVG"):
        svg_xml = example.get("xml", "")
        
        # Conta gli elementi
        elements = count_svg_elements(svg_xml)
        for elem, count in elements.items():
            element_counts[elem] += count
        
        # Estrai i colori
        colors = extract_colors(svg_xml)
        for color in colors:
            color_counts[color] += 1
        
        # Estrai la complessità dei path
        path_complexity = extract_path_complexity(svg_xml)
        path_stats["num_paths"].append(path_complexity["num_paths"])
        if path_complexity["num_paths"] > 0:
            path_stats["avg_commands"].append(path_complexity["avg_commands"])
            path_stats["max_commands"].append(path_complexity["max_commands"])
            path_stats["min_commands"].append(path_complexity["min_commands"])
        
        # Estrai le dimensioni
        dims = extract_svg_dimensions(svg_xml)
        if isinstance(dims["width"], (int, float)):
            dimensions["width"].append(dims["width"])
        if isinstance(dims["height"], (int, float)):
            dimensions["height"].append(dims["height"])
    
    # Calcola le statistiche
    logger.info("Calcolo delle statistiche...")
    
    # Statistiche sugli elementi
    element_stats = {
        "total": sum(element_counts.values()),
        "unique": len(element_counts),
        "most_common": element_counts.most_common(10)
    }
    
    # Statistiche sui colori
    color_stats = {
        "total": sum(color_counts.values()),
        "unique": len(color_counts),
        "most_common": color_counts.most_common(10)
    }
    
    # Statistiche sui path
    path_summary = {
        "avg_paths_per_svg": np.mean(path_stats["num_paths"]),
        "median_paths_per_svg": np.median(path_stats["num_paths"]),
        "max_paths": max(path_stats["num_paths"]),
        "min_paths": min(path_stats["num_paths"]),
        "avg_commands_per_path": np.mean(path_stats["avg_commands"]) if path_stats["avg_commands"] else 0,
        "max_commands": max(path_stats["max_commands"]) if path_stats["max_commands"] else 0
    }
    
    # Statistiche sulle dimensioni
    dimension_summary = {
        "avg_width": np.mean(dimensions["width"]) if dimensions["width"] else 0,
        "avg_height": np.mean(dimensions["height"]) if dimensions["height"] else 0,
        "median_width": np.median(dimensions["width"]) if dimensions["width"] else 0,
        "median_height": np.median(dimensions["height"]) if dimensions["height"] else 0,
        "max_width": max(dimensions["width"]) if dimensions["width"] else 0,
        "max_height": max(dimensions["height"]) if dimensions["height"] else 0,
        "min_width": min(dimensions["width"]) if dimensions["width"] else 0,
        "min_height": min(dimensions["height"]) if dimensions["height"] else 0
    }
    
    # Salva le statistiche in un file JSON
    stats = {
        "dataset_size": len(dataset),
        "elements": element_stats,
        "colors": color_stats,
        "paths": path_summary,
        "dimensions": dimension_summary
    }
    
    stats_path = os.path.join(output_dir, "svg_stats.json")
    with open(stats_path, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2)
    logger.info(f"Statistiche salvate in: {stats_path}")
    
    # Genera grafici
    logger.info("Generazione dei grafici...")
    
    # Grafico della distribuzione degli elementi
    plt.figure(figsize=(12, 6))
    elements = [elem for elem, count in element_counts.most_common(10)]
    counts = [count for elem, count in element_counts.most_common(10)]
    plt.bar(elements, counts)
    plt.title("Top 10 SVG Elements")
    plt.xlabel("Element")
    plt.ylabel("Count")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "element_distribution.png"), dpi=300)
    plt.close()
    
    # Grafico della distribuzione dei colori
    plt.figure(figsize=(12, 6))
    colors = [color for color, count in color_counts.most_common(10)]
    counts = [count for color, count in color_counts.most_common(10)]
    plt.bar(colors, counts)
    plt.title("Top 10 Colors")
    plt.xlabel("Color")
    plt.ylabel("Count")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "color_distribution.png"), dpi=300)
    plt.close()
    
    # Grafico della distribuzione del numero di path
    plt.figure(figsize=(10, 6))
    plt.hist(path_stats["num_paths"], bins=20)
    plt.title("Distribution of Path Count per SVG")
    plt.xlabel("Number of Paths")
    plt.ylabel("Frequency")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "path_count_distribution.png"), dpi=300)
    plt.close()
    
    # Grafico della distribuzione della complessità dei path
    if path_stats["avg_commands"]:
        plt.figure(figsize=(10, 6))
        plt.hist(path_stats["avg_commands"], bins=20)
        plt.title("Distribution of Average Commands per Path")
        plt.xlabel("Average Commands")
        plt.ylabel("Frequency")
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "path_complexity_distribution.png"), dpi=300)
        plt.close()
    
    # Grafico della distribuzione delle dimensioni
    if dimensions["width"] and dimensions["height"]:
        plt.figure(figsize=(10, 6))
        plt.scatter(dimensions["width"], dimensions["height"], alpha=0.5)
        plt.title("SVG Dimensions")
        plt.xlabel("Width")
        plt.ylabel("Height")
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "dimension_distribution.png"), dpi=300)
        plt.close()
    
    logger.info(f"Analisi completata. Risultati salvati in: {output_dir}")

def main():
    parser = argparse.ArgumentParser(description="Analizza un dataset di SVG.")
    parser.add_argument("--dataset_path", type=str, required=True, help="Percorso al file JSON/JSONL del dataset.")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/data_preparation/analysis", help="Directory di output per i risultati.")
    
    args = parser.parse_args()
    analyze_dataset(args.dataset_path, args.output_dir)

if __name__ == "__main__":
    main()
