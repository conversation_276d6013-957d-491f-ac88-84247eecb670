#!/usr/bin/env python3
"""
🧹 SCRIPT PULIZIA QUOTA SICURO - USO QUOTIDIANO
Pulizia conservativa che NON tocca mai checkpoint attivi
"""

import os
import shutil
import glob
from datetime import datetime, <PERSON><PERSON><PERSON>

def get_size_mb(path):
    """Calcola dimensione in MB"""
    if os.path.isfile(path):
        return os.path.getsize(path) / (1024 * 1024)
    elif os.path.isdir(path):
        total = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total += os.path.getsize(filepath)
                except (OSError, IOError):
                    pass
        return total / (1024 * 1024)
    return 0

def clean_cache_dirs():
    """Pulisce directory cache rigenerabili"""
    cache_dirs = [
        os.path.expanduser("~/.cache/huggingface"),
        os.path.expanduser("~/.cache/torch"),
        os.path.expanduser("~/.cache/pip"),
        "/tmp/pytorch_*",
        "/tmp/transformers_*"
    ]
    
    total_freed = 0
    
    for cache_pattern in cache_dirs:
        if "*" in cache_pattern:
            # Pattern con wildcard
            for cache_dir in glob.glob(cache_pattern):
                if os.path.exists(cache_dir):
                    size = get_size_mb(cache_dir)
                    try:
                        shutil.rmtree(cache_dir)
                        total_freed += size
                        print(f"🗑️ Rimosso: {cache_dir} ({size:.1f} MB)")
                    except Exception as e:
                        print(f"⚠️ Errore rimozione {cache_dir}: {e}")
        else:
            # Directory singola
            if os.path.exists(cache_pattern):
                size = get_size_mb(cache_pattern)
                try:
                    shutil.rmtree(cache_pattern)
                    total_freed += size
                    print(f"🗑️ Rimosso: {cache_pattern} ({size:.1f} MB)")
                except Exception as e:
                    print(f"⚠️ Errore rimozione {cache_pattern}: {e}")
    
    return total_freed

def clean_old_logs():
    """Pulisce log vecchi (>7 giorni)"""
    log_dirs = ["logs", "evaluation_results"]
    total_freed = 0
    cutoff_date = datetime.now() - timedelta(days=7)
    
    for log_dir in log_dirs:
        if os.path.exists(log_dir):
            for root, dirs, files in os.walk(log_dir):
                for file in files:
                    filepath = os.path.join(root, file)
                    try:
                        file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                        if file_time < cutoff_date and file.endswith(('.out', '.err', '.log')):
                            size = get_size_mb(filepath)
                            os.remove(filepath)
                            total_freed += size
                            print(f"🗑️ Log vecchio: {filepath} ({size:.1f} MB)")
                    except Exception as e:
                        print(f"⚠️ Errore log {filepath}: {e}")
    
    return total_freed

def clean_temp_files():
    """Pulisce file temporanei"""
    temp_patterns = [
        "/tmp/tmp*",
        "/tmp/pytorch_*",
        "/tmp/transformers_*",
        "*.tmp",
        "*.temp",
        "*~",
        ".DS_Store"
    ]
    
    total_freed = 0
    
    for pattern in temp_patterns:
        for filepath in glob.glob(pattern, recursive=True):
            try:
                size = get_size_mb(filepath)
                if os.path.isfile(filepath):
                    os.remove(filepath)
                elif os.path.isdir(filepath):
                    shutil.rmtree(filepath)
                total_freed += size
                print(f"🗑️ Temp: {filepath} ({size:.1f} MB)")
            except Exception as e:
                print(f"⚠️ Errore temp {filepath}: {e}")
    
    return total_freed

def check_active_jobs():
    """Verifica job attivi per evitare conflitti"""
    try:
        import subprocess
        result = subprocess.run(['squeue', '-u', 'ediluzio'], 
                              capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            active_jobs = result.stdout.strip().split('\n')[1:]  # Skip header
            if active_jobs and active_jobs[0].strip():
                print(f"⚠️ Job attivi rilevati: {len(active_jobs)}")
                print("🔒 Pulizia extra-conservativa attivata")
                return True
        return False
    except:
        print("⚠️ Impossibile verificare job attivi - modalità conservativa")
        return True

def main():
    print("🧹 PULIZIA QUOTA SICURA - USO QUOTIDIANO")
    print("=" * 50)
    print("✅ Sicurezza: MASSIMA - Non tocca mai checkpoint attivi")
    print("🎯 Target: Cache, log vecchi, file temporanei")
    print("=" * 50)
    
    # Verifica job attivi
    active_jobs = check_active_jobs()
    
    total_freed = 0
    
    # 1. Cache directories
    print("\n🗂️ Pulizia cache directories...")
    freed = clean_cache_dirs()
    total_freed += freed
    print(f"   💾 Liberati: {freed:.1f} MB")
    
    # 2. Log vecchi
    print("\n📋 Pulizia log vecchi (>7 giorni)...")
    freed = clean_old_logs()
    total_freed += freed
    print(f"   💾 Liberati: {freed:.1f} MB")
    
    # 3. File temporanei
    print("\n🗃️ Pulizia file temporanei...")
    freed = clean_temp_files()
    total_freed += freed
    print(f"   💾 Liberati: {freed:.1f} MB")
    
    print("\n" + "=" * 50)
    print(f"🎉 PULIZIA COMPLETATA!")
    print(f"💾 Spazio totale liberato: {total_freed:.1f} MB ({total_freed/1024:.2f} GB)")
    print("✅ Checkpoint attivi: PRESERVATI")
    print("✅ Training in corso: NON DISTURBATI")
    print("=" * 50)
    
    if total_freed < 100:
        print("\n💡 SUGGERIMENTO:")
        print("   Se serve più spazio, usa: python clean_quota_emergency.py")
        print("   (Richiede conferma, più aggressivo ma sicuro)")

if __name__ == "__main__":
    main()
