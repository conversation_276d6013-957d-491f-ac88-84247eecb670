# File temporanei o di sistema
*.log
*.tmp
*.swp
*.swo
.env
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.pytest_cache/
.coverage

# Ambiente virtuale
/svg_captioning_env/
venv/
env/
.venv/

# File binari o molto grandi (es. dataset)
*.zip
*.tar.gz
*.json
*.jsonl
*.csv
*.tsv
*.hdf5
*.h5
*.pt
*.bin
*.ckpt
*.safetensors

# Directory specifiche da escludere
/data/processed/
/data/raw/
/models/
/results/
/logs/
/.cache/
/.ipynb_checkpoints/

# Editor specifici
.vscode/
.idea/
*.sublime-*

# Eccezioni (file da includere nonostante le regole sopra)
!requirements.txt
!README.md
!ROADMAP.md
!FILESYSTEM_STRUCTURE.md
!.gitignore

# Includi i file di configurazione e gli script
!experiments/xml_direct_input/configs/*.json
!data/processed/README.md

# Andiamo nello specifico
/decoder_only/svg_dataset/svg_datasets/
/decoder_only/svg_dataset/logs/
/decoder_only/svg_dataset/models/