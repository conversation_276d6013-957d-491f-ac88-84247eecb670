# Esperimenti con Input XML Diretto

Questa directory contiene gli script e le configurazioni per gli esperimenti con input XML diretto.

## Script Principali

### Training

- **train_xml_slurm_token_xml.py**: Script principale per il training con tokenizer custom
- **train_xml_slurm.py**: Script per il training senza tokenizer custom
- **train_lora_8bit.slurm**: Script SLURM per il training LoRA in 8-bit
- **run_llama_test1_convergence.slurm**: Script SLURM per il training ottimizzato per convergenza di Llama 3.1 8B
- **run_gemma_test1_convergence.slurm**: Script SLURM per il training ottimizzato per convergenza di Gemma 2 9B IT

### Valutazione

- **evaluate_checkpoints.py**: Script per valutare i checkpoint con metriche multiple
- **run_checkpoint_evaluation.slurm**: Script SLURM per avviare la valutazione dei checkpoint
- **evaluate_zero_shot.py**: Script per valutare i modelli base in modalità zero-shot
- **run_evaluate_zero_shot.slurm**: Script SLURM per avviare la valutazione zero-shot
- **generate_evaluation_report.py**: Script per generare report HTML con valutazioni, grafici e esempi qualitativi
- **generate_qualitative_examples.py**: Script per generare esempi qualitativi dai modelli

### Inferenza

- **run_inference.py**: Script per l'inferenza zero-shot
- **run_inference_lora.py**: Script per l'inferenza con modelli fine-tuned con LoRA
- **run_inference_base_xml.py**: Script per l'inferenza con modelli base

### Utility

- **check_lora_targets.py**: Script per identificare i target modules per LoRA
- **run_complete_evaluation.sh**: Script per eseguire l'intera pipeline di valutazione
- **monitor_convergence_and_launch_custom_token.py**: Script per monitorare la convergenza e avviare il training successivo

## Configurazioni

La directory `configs/` contiene le configurazioni per i diversi modelli:

- **llama31_8b_instruct_lora_config.json**: Configurazione per Llama 3.1 8B
- **gemma2_9b_it_lora_config.json**: Configurazione per Gemma 2 9B IT
- **llama31_8b_custom_token_config.json**: Configurazione per Llama 3.1 8B con tokenizer personalizzato
- **gemma2_9b_it_custom_token_config.json**: Configurazione per Gemma 2 9B IT con tokenizer personalizzato
- **llama31_8b_test1_config.json**: Configurazione per Llama 3.1 8B con dataset diviso in fasce
- **gemma2_9b_it_test1_config.json**: Configurazione per Gemma 2 9B IT con dataset diviso in fasce
- **llama31_8b_test1_convergence_config.json**: Configurazione per Llama 3.1 8B ottimizzato per convergenza
- **gemma2_9b_it_test1_convergence_config.json**: Configurazione per Gemma 2 9B IT ottimizzato per convergenza
- **llama31_8b_custom_token_from_convergence.json**: Configurazione per Llama 3.1 8B con pesi convergenti e tokenizer personalizzato
- **gemma2_9b_it_custom_token_from_convergence.json**: Configurazione per Gemma 2 9B IT con pesi convergenti e tokenizer personalizzato

## Utilizzo

### Training LoRA

```bash
sbatch train_lora_8bit.slurm configs/MODEL_CONFIG.json
```

### Training Ottimizzato per Convergenza

```bash
sbatch run_llama_test1_convergence.slurm
sbatch run_gemma_test1_convergence.slurm
```

### Valutazione dei Checkpoint

```bash
sbatch run_checkpoint_evaluation.slurm MODEL_NAME CHECKPOINT_PATH TEST_FILE OUTPUT_DIR [CUSTOM_TOKENIZER] [LOAD_IN_8BIT] [LOAD_IN_4BIT] [NUM_SAMPLES] [USE_CLIP]
```

### Valutazione Zero-Shot

```bash
sbatch run_evaluate_zero_shot.slurm MODEL_NAME TEST_FILE OUTPUT_DIR [LOAD_IN_8BIT] [LOAD_IN_4BIT] [NUM_SAMPLES] [USE_CLIP] [WANDB_ENTITY] [WANDB_PROJECT]
```

### Generazione Report HTML

```bash
python generate_evaluation_report.py --output_dir REPORTS_DIR --models_dir MODELS_DIR --metrics_dir METRICS_DIR --examples_file EXAMPLES_FILE [--title TITLE] [--num_examples NUM_EXAMPLES] [--include_zero_shot] [--zero_shot_metrics ZERO_SHOT_METRICS] [--zero_shot_examples ZERO_SHOT_EXAMPLES]
```

### Inferenza Zero-Shot

```bash
sbatch run_inference.slurm MODEL_NAME /path/to/output.jsonl
```

### Inferenza LoRA

```bash
sbatch run_inference_lora.slurm
```

### Valutazione Completa

```bash
bash run_complete_evaluation.sh
```
