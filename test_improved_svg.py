#!/usr/bin/env python3
"""
Test SVG conversion migliorata
"""

import json

def xml_to_svg_improved(xml_content):
    """Converte XML content in SVG completo con miglioramenti visibilità"""
    try:
        # Parsing delle linee XML
        lines = xml_content.strip().split('\n')
        svg_elements = []
        
        for line in lines:
            if '\t' in line:
                style_part, d_part = line.split('\t', 1)
                style = style_part.replace('style=', '').strip()
                d = d_part.replace('d=', '').strip()
                
                # Migliora la visibilità
                # Aumenta stroke-width da 1 a 3
                style = style.replace('stroke-width:1', 'stroke-width:3')
                
                # Converti colori molto scuri in nero pieno
                if 'stroke:rgb(2,2,2)' in style:
                    style = style.replace('stroke:rgb(2,2,2)', 'stroke:rgb(0,0,0)')
                
                # Aggiungi fill per elementi che dovrebbero essere pieni
                if 'fill:none' in style and 'stroke:rgb(0,0,0)' in style:
                    # Per elementi neri, aggiungi anche fill nero
                    style = style.replace('fill:none', 'fill:rgb(0,0,0);fill-opacity:0.3')
                
                svg_elements.append(f'<path style="{style}" d="{d}"/>')
        
        # Crea SVG completo con sfondo bianco
        svg_content = f'''<svg width="400" height="400" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
<rect width="100%" height="100%" fill="white"/>
{chr(10).join(svg_elements)}
</svg>'''
        
        return svg_content
    except Exception as e:
        print(f"Errore conversione XML→SVG: {e}")
        return None

# Carica primo esempio
with open('data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB.json', 'r') as f:
    dataset = json.load(f)

first_example = dataset[0]
xml_content = first_example['xml']

svg_improved = xml_to_svg_improved(xml_content)
print("🖼️ SVG Migliorato:")
print(svg_improved)

# Salva SVG migliorato per test
with open('test_example_improved.svg', 'w') as f:
    f.write(svg_improved)

print("\n✅ SVG migliorato salvato in test_example_improved.svg")
print("📊 Miglioramenti applicati:")
print("   - Stroke-width: 1 → 3 (linee più spesse)")
print("   - Colore: rgb(2,2,2) → rgb(0,0,0) (nero pieno)")
print("   - Fill: none → fill con opacity per visibilità")
print("   - Sfondo: bianco esplicito")
