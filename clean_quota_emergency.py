#!/usr/bin/env python3
"""
🚨 SCRIPT PULIZIA QUOTA EMERGENZA - SOLO CRISI
Pulizia aggressiva ma sicura - mantiene ultimi 2 checkpoint
"""

import os
import shutil
import glob
from datetime import datetime, timed<PERSON><PERSON>

def get_size_mb(path):
    """Calcola dimensione in MB"""
    if os.path.isfile(path):
        return os.path.getsize(path) / (1024 * 1024)
    elif os.path.isdir(path):
        total = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total += os.path.getsize(filepath)
                except (OSError, IOError):
                    pass
        return total / (1024 * 1024)
    return 0

def clean_old_checkpoints():
    """Pulisce checkpoint vecchi mantenendo ultimi 2"""
    checkpoint_dirs = [
        "experiments/xml_direct_input/outputs",
        "models"
    ]
    
    total_freed = 0
    
    for base_dir in checkpoint_dirs:
        if not os.path.exists(base_dir):
            continue
            
        for model_dir in os.listdir(base_dir):
            model_path = os.path.join(base_dir, model_dir)
            if not os.path.isdir(model_path):
                continue
            
            # Trova checkpoint
            checkpoints = []
            for item in os.listdir(model_path):
                item_path = os.path.join(model_path, item)
                if os.path.isdir(item_path) and ("checkpoint" in item.lower() or item.startswith("step")):
                    try:
                        # Ordina per data modifica
                        mtime = os.path.getmtime(item_path)
                        checkpoints.append((mtime, item_path, item))
                    except:
                        pass
            
            # Ordina per data (più recenti prima)
            checkpoints.sort(reverse=True)
            
            # Mantieni solo ultimi 2
            if len(checkpoints) > 2:
                for _, checkpoint_path, checkpoint_name in checkpoints[2:]:
                    size = get_size_mb(checkpoint_path)
                    try:
                        shutil.rmtree(checkpoint_path)
                        total_freed += size
                        print(f"🗑️ Checkpoint vecchio: {model_dir}/{checkpoint_name} ({size:.1f} MB)")
                    except Exception as e:
                        print(f"⚠️ Errore checkpoint {checkpoint_path}: {e}")
    
    return total_freed

def clean_evaluation_results():
    """Pulisce risultati evaluation vecchi (>3 giorni)"""
    eval_dirs = ["evaluation_results"]
    total_freed = 0
    cutoff_date = datetime.now() - timedelta(days=3)
    
    for eval_dir in eval_dirs:
        if os.path.exists(eval_dir):
            for root, dirs, files in os.walk(eval_dir):
                for file in files:
                    filepath = os.path.join(root, file)
                    try:
                        file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                        if file_time < cutoff_date:
                            size = get_size_mb(filepath)
                            os.remove(filepath)
                            total_freed += size
                            print(f"🗑️ Evaluation vecchia: {filepath} ({size:.1f} MB)")
                    except Exception as e:
                        print(f"⚠️ Errore evaluation {filepath}: {e}")
    
    return total_freed

def clean_huggingface_cache():
    """Pulisce cache HuggingFace aggressivamente"""
    hf_cache = os.path.expanduser("~/.cache/huggingface")
    total_freed = 0
    
    if os.path.exists(hf_cache):
        # Mantieni solo modelli usati di recente (7 giorni)
        cutoff_date = datetime.now() - timedelta(days=7)
        
        for root, dirs, files in os.walk(hf_cache):
            for file in files:
                filepath = os.path.join(root, file)
                try:
                    file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                    if file_time < cutoff_date:
                        size = get_size_mb(filepath)
                        os.remove(filepath)
                        total_freed += size
                        if size > 10:  # Log solo file grandi
                            print(f"🗑️ HF Cache: {os.path.basename(filepath)} ({size:.1f} MB)")
                except Exception as e:
                    pass  # Ignora errori per cache
    
    return total_freed

def clean_wandb_cache():
    """Pulisce cache WandB"""
    wandb_dirs = [
        os.path.expanduser("~/.cache/wandb"),
        "wandb"
    ]
    
    total_freed = 0
    
    for wandb_dir in wandb_dirs:
        if os.path.exists(wandb_dir):
            size = get_size_mb(wandb_dir)
            try:
                shutil.rmtree(wandb_dir)
                total_freed += size
                print(f"🗑️ WandB Cache: {wandb_dir} ({size:.1f} MB)")
            except Exception as e:
                print(f"⚠️ Errore WandB {wandb_dir}: {e}")
    
    return total_freed

def check_active_jobs():
    """Verifica job attivi"""
    try:
        import subprocess
        result = subprocess.run(['squeue', '-u', 'ediluzio'], 
                              capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            active_jobs = result.stdout.strip().split('\n')[1:]
            if active_jobs and active_jobs[0].strip():
                return len(active_jobs)
        return 0
    except:
        return -1

def main():
    print("🚨 PULIZIA QUOTA EMERGENZA - SOLO CRISI")
    print("=" * 50)
    print("⚠️ ATTENZIONE: Pulizia aggressiva ma sicura")
    print("✅ Sicurezza: ALTA - Mantiene ultimi 2 checkpoint")
    print("🎯 Target: Checkpoint vecchi, cache, evaluation")
    print("=" * 50)
    
    # Verifica job attivi
    active_jobs = check_active_jobs()
    if active_jobs > 0:
        print(f"⚠️ Job attivi rilevati: {active_jobs}")
        print("🔒 Protezione checkpoint attivi ATTIVA")
    
    # Richiedi conferma
    print("\n🚨 CONFERMA RICHIESTA:")
    print("   Questa pulizia è più aggressiva del normale")
    print("   Rimuoverà checkpoint vecchi (mantiene ultimi 2)")
    print("   Rimuoverà cache e evaluation vecchie")
    
    confirm = input("\n❓ Procedere? (scrivi 'CONFERMA' per continuare): ")
    
    if confirm != "CONFERMA":
        print("❌ Operazione annullata")
        return
    
    print("\n🚀 Avvio pulizia emergenza...")
    total_freed = 0
    
    # 1. Checkpoint vecchi
    print("\n🗂️ Pulizia checkpoint vecchi (mantiene ultimi 2)...")
    freed = clean_old_checkpoints()
    total_freed += freed
    print(f"   💾 Liberati: {freed:.1f} MB")
    
    # 2. Evaluation vecchie
    print("\n📊 Pulizia evaluation vecchie (>3 giorni)...")
    freed = clean_evaluation_results()
    total_freed += freed
    print(f"   💾 Liberati: {freed:.1f} MB")
    
    # 3. Cache HuggingFace
    print("\n🤗 Pulizia cache HuggingFace (>7 giorni)...")
    freed = clean_huggingface_cache()
    total_freed += freed
    print(f"   💾 Liberati: {freed:.1f} MB")
    
    # 4. Cache WandB
    print("\n📈 Pulizia cache WandB...")
    freed = clean_wandb_cache()
    total_freed += freed
    print(f"   💾 Liberati: {freed:.1f} MB")
    
    print("\n" + "=" * 50)
    print(f"🎉 PULIZIA EMERGENZA COMPLETATA!")
    print(f"💾 Spazio totale liberato: {total_freed:.1f} MB ({total_freed/1024:.2f} GB)")
    print("✅ Ultimi 2 checkpoint: PRESERVATI")
    print("✅ Training attivi: PROTETTI")
    print("=" * 50)
    
    if total_freed > 1000:
        print(f"\n🎉 Ottimo! Liberati {total_freed/1024:.2f} GB")
    elif total_freed > 100:
        print(f"\n👍 Buono! Liberati {total_freed:.1f} MB")
    else:
        print(f"\n💡 Poco spazio liberato. Il sistema è già pulito!")

if __name__ == "__main__":
    main()
